[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "chatbot-backend"
version = "0.1.0"
description = "聊天机器人后端 API 服务"
requires-python = ">=3.9"
license = "MIT"
authors = [
    { name = "Developer", email = "<EMAIL>" },
]
keywords = ["chatbot", "api", "fastapi", "ai"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
]

dependencies = [
    "fastapi>=0.116.0",
    "uvicorn[standard]>=0.35.0",
    "sqlalchemy>=2.0.23",
    "python-multipart>=0.0.6",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "python-dotenv>=1.0.0",
    "alembic>=1.12.1",
    "httpx>=0.25.2",
    "pymilvus>=2.3.0",
    "openai>=1.0.0",
    "requests>=2.31.0",
    "pandas>=2.0.0",
    "ldap3>=2.9.1",
    "pymilvus"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.3.0",
]

test = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "httpx>=0.25.2",
]

[project.urls]
Homepage = "https://github.com/yourusername/chatbot-ui"
Documentation = "https://github.com/yourusername/chatbot-ui#readme"
Repository = "https://github.com/yourusername/chatbot-ui.git"
Issues = "https://github.com/yourusername/chatbot-ui/issues"

[project.scripts]
chatbot-server = "main:app"

[tool.hatch.build.targets.wheel]
packages = ["api", "config", "db", "models", "schemas", "services", "utils"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
  | __pycache__
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["api", "config", "db", "models", "schemas", "services", "utils"]

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_functions = ["test_*"]
python_classes = ["Test*"]

[tool.coverage.run]
source = ["."]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "pymilvus.*",
    "alembic.*",
]
ignore_missing_imports = true 
