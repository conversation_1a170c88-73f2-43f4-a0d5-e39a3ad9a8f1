#  聊天系统RAG问答链路详细设计文档

---

## 一、需求背景

基于现有聊天系统，集成LangChain构建RAG（Retrieval-Augmented Generation）问答链路，实现智能问答功能。系统需要：

1. **使用LangChain构建RAG问答链路**：集成现有的Milvus向量数据库检索功能
2. **流式回答**：通过/chat/stream接口实现SSE流式返回
3. **向量检索**：使用backend\services\milvus_service.py中的search_hybrid_parent_child方法

### 1.1 技术栈

* **前端**：Vue3 + TailwindCSS + shadcn-ui + Lucide Icons + Framer Motion
* **后端**：FastAPI + SQLAlchemy + SQLite + LangChain + Milvus
* **AI模型**：OpenAI API + BGE-M3嵌入模型 + BGE-Reranker-V2-M3重排序模型
* **向量数据库**：Milvus（支持混合检索：语义向量 + BM25稀疏向量）
* **通信**：基于HTTP的REST接口 + SSE流

---

## 二、系统架构设计

### 2.1 整体架构图

```
┌─────────────────┐    HTTP/SSE    ┌─────────────────┐
│   Vue3 前端     │ ◄─────────────► │  FastAPI 后端   │
│                 │                │                 │
│ - 聊天界面      │                │ - 认证授权      │
│ - 流式消息显示  │                │ - 会话管理      │
│ - 实时渲染      │                │ - RAG问答链路   │
└─────────────────┘                └─────────────────┘
                                            │
                                            ▼
                                   ┌─────────────────┐
                                   │ LangChain RAG   │
                                   │                 │
                                   │ ┌─────────────┐ │
                                   │ │ Retriever   │ │
                                   │ │             │ │
                                   │ │ Milvus      │ │
                                   │ │ Service     │ │
                                   │ └─────────────┘ │
                                   │        │        │
                                   │        ▼        │
                                   │ ┌─────────────┐ │
                                   │ │ Prompt      │ │
                                   │ │ Template    │ │
                                   │ └─────────────┘ │
                                   │        │        │
                                   │        ▼        │
                                   │ ┌─────────────┐ │
                                   │ │ LLM         │ │
                                   │ │ (OpenAI)    │ │
                                   │ └─────────────┘ │
                                   │        │        │
                                   │        ▼        │
                                   │ ┌─────────────┐ │
                                   │ │ Output      │ │
                                   │ │ Parser      │ │
                                   │ └─────────────┘ │
                                   └─────────────────┘
                                            │
                                            ▼
                                   ┌─────────────────┐
                                   │ Milvus 向量DB   │
                                   │                 │
                                   │ - 混合检索      │
                                   │ - 父子分块      │
                                   │ - 重排序        │
                                   └─────────────────┘
```

### 2.2 模块划分

| 模块名 | 功能 | 主要组件 |
|--------|------|----------|
| auth | 登录认证模块 | LDAP认证、JWT Token |
| chat | 聊天会话模块 | 会话管理、消息存储 |
| rag | RAG问答模块 | LangChain链路、向量检索、文档处理 |
| milvus | 向量数据库服务 | 混合检索、重排序、时间加权 |
| message | 消息处理模块 | 流式响应、消息存储 |
| ui | 前端UI交互模块 | 实时消息显示、流式渲染 |
| api | 前后端通信模块 | REST接口、SSE流 |
| db | 数据模型模块 | SQLAlchemy模型、数据访问 |

---

## 三、RAG问答链路设计

### 3.1 LangChain组件架构

```python
# RAG链路组件设计
RAG_Chain = (
    Retriever +           # 向量检索器（基于milvus_service）
    PromptTemplate +      # 提示词模板
    LLM +                 # 大语言模型（OpenAI）
    OutputParser          # 输出解析器
)
```

### 3.2 核心组件详细设计

#### 3.2.1 自定义Retriever（基于milvus_service）

```python
class MilvusRetriever(BaseRetriever):
    """基于现有milvus_service的自定义检索器"""
    
    def __init__(self, milvus_service: milvus_service):
        self.milvus_service = milvus_service
        
    def _get_relevant_documents(self, query: str) -> List[Document]:
        """使用search_hybrid_parent_child进行检索"""
        # 调用现有的混合检索方法
        results = self.milvus_service.search_hybrid_parent_child(
            query=query,
            k=5,
            semantic_weight=0.7,
            min_score=0.1,
            sort_by="max_score"
        )
        
        # 转换为LangChain Document格式
        documents = []
        for result in results:
            # 组合父文档和子文档内容
            content = self._combine_parent_child_content(result)
            metadata = self._extract_metadata(result)
            documents.append(Document(page_content=content, metadata=metadata))
            
        return documents
```

#### 3.2.2 提示词模板设计

```python
RAG_PROMPT_TEMPLATE = """
你是一个专业的AI助手，请基于以下检索到的相关文档内容来回答用户的问题。

相关文档：
{context}

用户问题：{question}

请根据相关文档内容提供准确、详细的回答。如果文档中没有相关信息，请明确说明。

回答：
"""
```

#### 3.2.3 流式输出处理

```python
class StreamingRAGChain:
    """支持流式输出的RAG链路"""
    
    def __init__(self, retriever, llm, prompt_template):
        self.retriever = retriever
        self.llm = llm
        self.prompt_template = prompt_template
        
    async def astream(self, query: str) -> AsyncGenerator[str, None]:
        """异步流式生成回答"""
        # 1. 检索相关文档
        documents = await self.retriever.aget_relevant_documents(query)
        
        # 2. 构建上下文
        context = self._format_documents(documents)
        
        # 3. 构建提示词
        prompt = self.prompt_template.format(
            context=context,
            question=query
        )
        
        # 4. 流式生成回答
        async for chunk in self.llm.astream(prompt):
            yield chunk.content
```

---

## 四、接口设计

### 4.1 RAG服务接口

#### 4.1.1 RAG服务类设计

```python
class RAGService:
    """RAG问答服务"""
    
    def __init__(self, milvus_service: milvus_service, openai_client):
        self.milvus_service = milvus_service
        self.retriever = MilvusRetriever(milvus_service)
        self.llm = ChatOpenAI(client=openai_client, streaming=True)
        self.rag_chain = StreamingRAGChain(
            retriever=self.retriever,
            llm=self.llm,
            prompt_template=RAG_PROMPT_TEMPLATE
        )
    
    async def generate_streaming_response(
        self, 
        query: str,
        conversation_history: List[Dict] = None
    ) -> AsyncGenerator[str, None]:
        """生成流式RAG回答"""
        # 可选：结合对话历史
        enhanced_query = self._enhance_query_with_history(query, conversation_history)
        
        # 流式生成回答
        async for chunk in self.rag_chain.astream(enhanced_query):
            yield chunk
```

#### 4.1.2 更新后的chat.py接口

```python
@router.post("/chat/stream", summary="发送消息（RAG流式回答）")
async def chat_stream(
    chat_data: ChatRequest,
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session),
):
    """发送消息并以SSE流的形式返回RAG回复"""

    async def generate_response():
        try:
            # 1. 验证用户权限并保存用户消息
            user_message = message_service.create_user_message(
                db, chat_data.conversation_id, chat_data.message, current_username
            )

            # 2. 获取对话历史（可选）
            conversation_history = message_service.get_conversation_messages(
                db, chat_data.conversation_id, current_username
            )

            # 3. 使用RAG服务生成流式回答
            ai_response_content = ""
            async for chunk in rag_service.generate_streaming_response(
                chat_data.message, 
                conversation_history
            ):
                ai_response_content += chunk
                # 转义JSON特殊字符
                escaped_chunk = chunk.replace('"', '\\"').replace('\n', '\\n')
                yield f'data: {{"content": "{escaped_chunk}"}}\n\n'

            # 4. 保存AI回复到数据库
            message_service.create_assistant_message(
                db, chat_data.conversation_id, ai_response_content
            )

            yield "data: [DONE]\n\n"

        except Exception as e:
            yield f'data: {{"error": "处理消息时发生错误: {str(e)}"}}\n\n'

    return StreamingResponse(
        generate_response(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Credentials": "true",
        },
    )
```

---

## 五、数据流程设计

### 5.1 RAG问答完整流程

```
1. 用户输入问题
   ↓
2. 前端发送POST请求到/api/chat/stream
   ↓
3. 后端验证用户身份和权限
   ↓
4. 保存用户消息到数据库
   ↓
5. RAG服务处理：
   5.1 使用MilvusRetriever检索相关文档
       - 调用search_hybrid_parent_child
       - 混合检索（语义+关键词）
       - 父子分块聚合
       - 重排序和过滤
   5.2 构建提示词上下文
   5.3 调用OpenAI API生成回答
   5.4 流式返回生成内容
   ↓
6. 后端通过SSE流式推送给前端
   ↓
7. 前端实时显示流式内容
   ↓
8. 生成完成后保存AI回复到数据库
```

### 5.2 错误处理流程

```
异常情况处理：
├── 向量检索失败
│   ├── Milvus连接异常 → 返回错误信息
│   ├── 检索超时 → 降级到简单回答
│   └── 无相关文档 → 基于通用知识回答
├── LLM调用失败
│   ├── API限流 → 重试机制
│   ├── 网络异常 → 错误提示
│   └── Token超限 → 截断上下文
└── 数据库操作失败
    ├── 连接异常 → 错误日志
    └── 权限问题 → 用户提示
```

---

## 六、技术实现细节

### 6.1 依赖包管理

#### 6.1.1 新增依赖

需要在`backend/pyproject.toml`中添加以下依赖：

```toml
dependencies = [
    # 现有依赖...
    "langchain>=0.1.0",
    "langchain-core>=0.1.0",
    "langchain-openai>=0.1.0",
    "langchain-community>=0.1.0",
    "tiktoken>=0.5.0",  # OpenAI tokenizer
]
```

#### 6.1.2 配置管理

扩展`backend/config/settings.py`：

```python
class Settings(BaseSettings):
    # 现有配置...

    # RAG配置
    RAG_ENABLED: bool = True
    RAG_RETRIEVAL_K: int = 5
    RAG_SEMANTIC_WEIGHT: float = 0.7
    RAG_MIN_SCORE: float = 0.1
    RAG_SORT_BY: str = "max_score"

    # Milvus配置
    MILVUS_URL: str = "http://localhost:19530"
    MILVUS_USER: str = ""
    MILVUS_PASSWORD: str = ""
    MILVUS_DB_NAME: str = "default"
    MILVUS_COLLECTION_NAME: str = "documents"

    # OpenAI配置
    OPENAI_API_KEY: str = ""
    OPENAI_BASE_URL: str = "https://api.openai.com/v1"
    OPENAI_MODEL: str = "gpt-3.5-turbo"
    OPENAI_TEMPERATURE: float = 0.7
    OPENAI_MAX_TOKENS: int = 2000

    # BGE模型配置
    BGE_EMBEDDING_MODEL: str = "bge-m3"
    BGE_RERANK_MODEL: str = "bge-reranker-v2-m3"
    BGE_RERANK_URL: str = "http://localhost:8001/rerank"
```

### 6.2 服务初始化

#### 6.2.1 RAG服务工厂

```python
# backend/services/rag_factory.py
from typing import Optional
from langchain_openai import ChatOpenAI
from config.settings import settings
from services.milvus_service import milvus_service
from services.rag_service import RAGService

class RAGServiceFactory:
    """RAG服务工厂类"""

    _instance: Optional[RAGService] = None

    @classmethod
    def get_rag_service(cls) -> RAGService:
        """获取RAG服务单例"""
        if cls._instance is None:
            cls._instance = cls._create_rag_service()
        return cls._instance

    @classmethod
    def _create_rag_service(cls) -> RAGService:
        """创建RAG服务实例"""
        # 初始化Milvus服务
        milvus_svc = milvus_service(
            milvus_url=settings.MILVUS_URL,
            milvus_user=settings.MILVUS_USER,
            milvus_password=settings.MILVUS_PASSWORD,
            db_name=settings.MILVUS_DB_NAME,
            collection_name=settings.MILVUS_COLLECTION_NAME,
            chunk_strategy="parent_child",
            embedding_model=settings.BGE_EMBEDDING_MODEL,
            rerank_url=settings.BGE_RERANK_URL,
            openai_url=settings.OPENAI_BASE_URL,
            openai_api_key=settings.OPENAI_API_KEY,
        )

        # 初始化OpenAI客户端
        llm = ChatOpenAI(
            api_key=settings.OPENAI_API_KEY,
            base_url=settings.OPENAI_BASE_URL,
            model=settings.OPENAI_MODEL,
            temperature=settings.OPENAI_TEMPERATURE,
            max_tokens=settings.OPENAI_MAX_TOKENS,
            streaming=True,
        )

        return RAGService(milvus_svc, llm)
```

### 6.3 文档处理和上下文构建

#### 6.3.1 文档格式化

```python
class DocumentFormatter:
    """文档格式化工具"""

    @staticmethod
    def format_parent_child_documents(search_results: List[Dict]) -> str:
        """格式化父子文档检索结果"""
        formatted_docs = []

        for i, result in enumerate(search_results, 1):
            parent_doc = result.get("parent_document", "")
            parent_metadata = result.get("parent_metadata", {})
            child_hits = result.get("child_hits", [])
            search_stats = result.get("search_stats", {})

            # 构建文档标题
            filename = parent_metadata.get("filename", f"文档{i}")
            doc_title = f"【文档{i}：{filename}】"

            # 添加相关性信息
            max_score = search_stats.get("max_score", 0)
            hit_count = search_stats.get("hit_count", 0)
            relevance_info = f"(相关性: {max_score:.3f}, 命中片段: {hit_count}个)"

            # 构建文档内容
            doc_content = f"{doc_title} {relevance_info}\n"
            doc_content += f"内容摘要: {parent_doc[:200]}...\n"

            # 添加高相关性的子文档片段
            if child_hits:
                doc_content += "相关片段:\n"
                for j, child in enumerate(child_hits[:3], 1):  # 最多显示3个片段
                    child_content = child.get("document", "")
                    child_score = child.get("score", 0)
                    doc_content += f"  片段{j} (分数: {child_score:.3f}): {child_content[:150]}...\n"

            formatted_docs.append(doc_content)

        return "\n" + "="*50 + "\n".join(formatted_docs) + "="*50 + "\n"
```

### 6.4 对话历史处理

#### 6.4.1 历史上下文增强

```python
class ConversationContextManager:
    """对话上下文管理器"""

    @staticmethod
    def enhance_query_with_history(
        current_query: str,
        conversation_history: List[Dict],
        max_history_turns: int = 3
    ) -> str:
        """使用对话历史增强当前查询"""
        if not conversation_history:
            return current_query

        # 获取最近的对话轮次
        recent_messages = conversation_history[-max_history_turns*2:]  # 用户+助手消息对

        # 构建上下文
        context_parts = []
        for msg in recent_messages:
            role = msg.get("role", "")
            content = msg.get("content", "")
            if role == "user":
                context_parts.append(f"用户之前问: {content}")
            elif role == "assistant":
                context_parts.append(f"助手回答: {content[:100]}...")

        if context_parts:
            enhanced_query = f"""
对话历史:
{chr(10).join(context_parts)}

当前问题: {current_query}

请结合对话历史理解当前问题的上下文含义。
"""
            return enhanced_query

        return current_query
```

### 6.5 性能优化

#### 6.5.1 缓存策略

```python
from functools import lru_cache
import hashlib

class RAGCache:
    """RAG结果缓存"""

    def __init__(self, max_size: int = 100):
        self.max_size = max_size
        self._cache = {}

    def _get_cache_key(self, query: str, params: Dict) -> str:
        """生成缓存键"""
        cache_data = f"{query}_{str(sorted(params.items()))}"
        return hashlib.md5(cache_data.encode()).hexdigest()

    def get(self, query: str, params: Dict) -> Optional[List[Dict]]:
        """获取缓存结果"""
        key = self._get_cache_key(query, params)
        return self._cache.get(key)

    def set(self, query: str, params: Dict, result: List[Dict]):
        """设置缓存结果"""
        if len(self._cache) >= self.max_size:
            # 简单的LRU策略：删除最旧的条目
            oldest_key = next(iter(self._cache))
            del self._cache[oldest_key]

        key = self._get_cache_key(query, params)
        self._cache[key] = result
```

#### 6.5.2 异步处理优化

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

class AsyncRAGService:
    """异步RAG服务"""

    def __init__(self, rag_service: RAGService):
        self.rag_service = rag_service
        self.executor = ThreadPoolExecutor(max_workers=4)

    async def async_retrieve_documents(self, query: str) -> List[Dict]:
        """异步文档检索"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor,
            self.rag_service.milvus_service.search_hybrid_parent_child,
            query
        )
```

---

## 七、部署和配置

### 7.1 环境配置

#### 7.1.1 环境变量配置文件(.env)

```bash
# 应用配置
APP_NAME=聊天系统RAG版本
DEBUG=false

# 数据库配置
DATABASE_URL=sqlite:///./database.db

# 安全配置
SECRET_KEY=your-super-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=60

# LDAP配置
LDAP_URI=ldap://**************:389
LDAP_BASE_DN=dc=users,dc=appdata,dc=erayt,dc=com

# RAG配置
RAG_ENABLED=true
RAG_RETRIEVAL_K=5
RAG_SEMANTIC_WEIGHT=0.7
RAG_MIN_SCORE=0.1
RAG_SORT_BY=max_score

# Milvus配置
MILVUS_URL=http://localhost:19530
MILVUS_USER=
MILVUS_PASSWORD=
MILVUS_DB_NAME=default
MILVUS_COLLECTION_NAME=documents

# OpenAI配置
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=2000

# BGE模型配置
BGE_EMBEDDING_MODEL=bge-m3
BGE_RERANK_MODEL=bge-reranker-v2-m3
BGE_RERANK_URL=http://localhost:8001/rerank
```

---

## 八、测试策略

### 8.1 单元测试

#### 8.1.1 RAG服务测试

```python
# tests/test_rag_service.py
import pytest
from unittest.mock import Mock, AsyncMock
from services.rag_service import RAGService
from services.milvus_service import milvus_service

class TestRAGService:
    """RAG服务测试"""

    @pytest.fixture
    def mock_milvus_service(self):
        """模拟Milvus服务"""
        mock_service = Mock(spec=milvus_service)
        mock_service.search_hybrid_parent_child.return_value = [
            {
                "parent_document": "测试文档内容",
                "parent_metadata": {"filename": "test.txt"},
                "child_hits": [
                    {"document": "相关片段1", "score": 0.8},
                    {"document": "相关片段2", "score": 0.7}
                ],
                "search_stats": {"max_score": 0.8, "hit_count": 2}
            }
        ]
        return mock_service

    @pytest.fixture
    def mock_llm(self):
        """模拟LLM"""
        mock_llm = AsyncMock()
        mock_llm.astream.return_value = [
            Mock(content="这是"),
            Mock(content="测试"),
            Mock(content="回答")
        ]
        return mock_llm

    @pytest.fixture
    def rag_service(self, mock_milvus_service, mock_llm):
        """RAG服务实例"""
        return RAGService(mock_milvus_service, mock_llm)

    @pytest.mark.asyncio
    async def test_generate_streaming_response(self, rag_service):
        """测试流式响应生成"""
        query = "测试问题"

        response_chunks = []
        async for chunk in rag_service.generate_streaming_response(query):
            response_chunks.append(chunk)

        assert len(response_chunks) == 3
        assert "".join(response_chunks) == "这是测试回答"

    def test_document_formatting(self, rag_service):
        """测试文档格式化"""
        search_results = [
            {
                "parent_document": "测试文档",
                "parent_metadata": {"filename": "test.txt"},
                "child_hits": [{"document": "片段", "score": 0.8}],
                "search_stats": {"max_score": 0.8, "hit_count": 1}
            }
        ]

        formatted = rag_service._format_documents(search_results)
        assert "测试文档" in formatted
        assert "test.txt" in formatted
        assert "0.8" in formatted
```

#### 8.1.2 Milvus检索器测试

```python
# tests/test_milvus_retriever.py
import pytest
from langchain.schema import Document
from services.rag_retriever import MilvusRetriever

class TestMilvusRetriever:
    """Milvus检索器测试"""

    @pytest.fixture
    def mock_milvus_service(self):
        mock_service = Mock()
        mock_service.search_hybrid_parent_child.return_value = [
            {
                "parent_document": "文档内容",
                "parent_metadata": {"filename": "test.txt", "id": 1},
                "child_hits": [{"document": "片段", "score": 0.8}]
            }
        ]
        return mock_service

    @pytest.fixture
    def retriever(self, mock_milvus_service):
        return MilvusRetriever(mock_milvus_service)

    def test_get_relevant_documents(self, retriever):
        """测试文档检索"""
        query = "测试查询"
        documents = retriever._get_relevant_documents(query)

        assert len(documents) == 1
        assert isinstance(documents[0], Document)
        assert "文档内容" in documents[0].page_content
        assert documents[0].metadata["filename"] == "test.txt"
```

### 8.2 集成测试

#### 8.2.1 端到端API测试

```python
# tests/test_chat_api.py
import pytest
from fastapi.testclient import TestClient
from main import app

class TestChatAPI:
    """聊天API集成测试"""

    @pytest.fixture
    def client(self):
        return TestClient(app)

    @pytest.fixture
    def auth_headers(self, client):
        """获取认证头"""
        # 模拟登录获取token
        response = client.post("/api/login", json={
            "username": "test_user",
            "password": "test_password"
        })
        token = response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}

    def test_chat_stream_endpoint(self, client, auth_headers):
        """测试流式聊天接口"""
        response = client.post(
            "/api/chat/stream",
            json={
                "conversation_id": 1,
                "message": "测试问题"
            },
            headers=auth_headers
        )

        assert response.status_code == 200
        assert response.headers["content-type"] == "text/event-stream; charset=utf-8"

        # 检查SSE流格式
        content = response.content.decode()
        assert "data: " in content
        assert "[DONE]" in content
```

### 8.3 性能测试

#### 8.3.1 负载测试脚本

```python
# tests/performance/load_test.py
import asyncio
import aiohttp
import time
from concurrent.futures import ThreadPoolExecutor

class LoadTester:
    """负载测试器"""

    def __init__(self, base_url: str, concurrent_users: int = 10):
        self.base_url = base_url
        self.concurrent_users = concurrent_users

    async def simulate_user_session(self, session: aiohttp.ClientSession, user_id: int):
        """模拟用户会话"""
        # 登录
        login_data = {
            "username": f"test_user_{user_id}",
            "password": "test_password"
        }

        async with session.post(f"{self.base_url}/api/login", json=login_data) as resp:
            if resp.status != 200:
                return {"user_id": user_id, "error": "登录失败"}

            token = (await resp.json())["access_token"]
            headers = {"Authorization": f"Bearer {token}"}

        # 发送多个问题
        questions = [
            "什么是人工智能？",
            "机器学习的基本原理是什么？",
            "深度学习和传统机器学习的区别？"
        ]

        results = []
        for question in questions:
            start_time = time.time()

            chat_data = {
                "conversation_id": user_id,
                "message": question
            }

            async with session.post(
                f"{self.base_url}/api/chat/stream",
                json=chat_data,
                headers=headers
            ) as resp:
                if resp.status == 200:
                    # 读取完整的SSE流
                    async for line in resp.content:
                        if b"[DONE]" in line:
                            break

                    response_time = time.time() - start_time
                    results.append({
                        "question": question,
                        "response_time": response_time,
                        "status": "success"
                    })
                else:
                    results.append({
                        "question": question,
                        "status": "error",
                        "error_code": resp.status
                    })

        return {"user_id": user_id, "results": results}

    async def run_load_test(self, duration_seconds: int = 60):
        """运行负载测试"""
        async with aiohttp.ClientSession() as session:
            tasks = []

            # 创建并发用户任务
            for user_id in range(self.concurrent_users):
                task = asyncio.create_task(
                    self.simulate_user_session(session, user_id)
                )
                tasks.append(task)

            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)

            return self.analyze_results(results)

    def analyze_results(self, results):
        """分析测试结果"""
        total_requests = 0
        successful_requests = 0
        total_response_time = 0
        errors = []

        for user_result in results:
            if isinstance(user_result, Exception):
                errors.append(str(user_result))
                continue

            for result in user_result.get("results", []):
                total_requests += 1
                if result["status"] == "success":
                    successful_requests += 1
                    total_response_time += result["response_time"]
                else:
                    errors.append(result.get("error", "未知错误"))

        success_rate = successful_requests / total_requests if total_requests > 0 else 0
        avg_response_time = total_response_time / successful_requests if successful_requests > 0 else 0

        return {
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "success_rate": success_rate,
            "average_response_time": avg_response_time,
            "errors": errors
        }

# 运行负载测试
if __name__ == "__main__":
    tester = LoadTester("http://localhost:8000", concurrent_users=20)
    results = asyncio.run(tester.run_load_test(duration_seconds=120))

    print("负载测试结果:")
    print(f"总请求数: {results['total_requests']}")
    print(f"成功请求数: {results['successful_requests']}")
    print(f"成功率: {results['success_rate']:.2%}")
    print(f"平均响应时间: {results['average_response_time']:.2f}秒")
    print(f"错误数: {len(results['errors'])}")
```
