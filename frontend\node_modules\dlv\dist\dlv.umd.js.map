{"version": 3, "file": "dlv.umd.js", "sources": ["../index.js"], "sourcesContent": ["export default function dlv(obj, key, def, p, undef) {\n\tkey = key.split ? key.split('.') : key;\n\tfor (p = 0; p < key.length; p++) {\n\t\tobj = obj ? obj[key[p]] : undef;\n\t}\n\treturn obj === undef ? def : obj;\n}\n"], "names": ["obj", "key", "def", "p", "undef", "split", "length"], "mappings": "mFAAe,SAAaA,EAAKC,EAAKC,EAAKC,EAAGC,OAC7CH,EAAMA,EAAII,MAAQJ,EAAII,MAAM,KAAOJ,EAC9BE,EAAI,EAAGA,EAAIF,EAAIK,OAAQH,IAC3BH,EAAMA,EAAMA,EAAIC,EAAIE,IAAMC,SAEpBJ,IAAQI,EAAQF,EAAMF,kEALf,SAAaA,EAAKC,EAAKC,EAAKC,EAAGC,OAC7CH,EAAMA,EAAII,MAAQJ,EAAII,MAAM,KAAOJ,EAC9BE,EAAI,EAAGA,EAAIF,EAAIK,OAAQH,IAC3BH,EAAMA,EAAMA,EAAIC,EAAIE,IAAMC,SAEpBJ,IAAQI,EAAQF,EAAMF,WALf,SAAaA,EAAKC,EAAKC,EAAKC,EAAGC,OAC7CH,EAAMA,EAAII,MAAQJ,EAAII,MAAM,KAAOJ,EAC9BE,EAAI,EAAGA,EAAIF,EAAIK,OAAQH,IAC3BH,EAAMA,EAAMA,EAAIC,EAAIE,IAAMC,SAEpBJ,IAAQI,EAAQF,EAAMF"}