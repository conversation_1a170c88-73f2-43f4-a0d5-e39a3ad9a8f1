{"version": 3, "file": "Parser.d.ts", "sourceRoot": "", "sources": ["../../src/ts-eslint/Parser.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAC7D,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AACrD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAErC,yBAAiB,MAAM,CAAC;IACtB,UAAiB,UAAU;QACzB;;WAEG;QACH,IAAI,EAAE,MAAM,CAAC;QACb;;WAEG;QACH,OAAO,CAAC,EAAE,MAAM,CAAC;KAClB;IAED;;;;OAIG;IACH,KAAY,iBAAiB,GACzB;QACE;;WAEG;QACH,IAAI,CAAC,EAAE,UAAU,CAAC;QAClB;;WAEG;QACH,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC;KACjD,GACD;QACE;;WAEG;QACH,IAAI,CAAC,EAAE,UAAU,CAAC;QAClB;;WAEG;QACH,cAAc,CACZ,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE,OAAO,GAChB;aAEA,CAAC,IAAI,MAAM,WAAW,GAAG,OAAO;SAClC,CAAC;KACH,CAAC;IAEN,KAAY,YAAY,GACpB;QACE;;WAEG;QACH,IAAI,CAAC,EAAE,UAAU,CAAC;QAClB;;WAEG;QACH,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC;KAChE,GACD;QACE;;WAEG;QACH,IAAI,CAAC,EAAE,UAAU,CAAC;QAClB;;WAEG;QACH,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,aAAa,GAAG,WAAW,CAAC;KACpE,CAAC;IAEN,UAAiB,WAAW;QAC1B;;WAEG;QACH,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC;QACtB;;;;WAIG;QACH,QAAQ,CAAC,EAAE,cAAc,CAAC;QAC1B;;;;WAIG;QACH,YAAY,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC;QAClC;;;;;WAKG;QACH,WAAW,CAAC,EAAE,WAAW,CAAC;KAC3B;IAGD,UAAiB,WAAW;QAC1B,CAAC,QAAQ,EAAE,MAAM,GAAG,SAAS,MAAM,EAAE,CAAC;KACvC;CACF"}