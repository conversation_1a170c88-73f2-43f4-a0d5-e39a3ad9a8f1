/**
 * lucide-vue-next v0.292.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const ArrowUpFromDot = createLucideIcon("ArrowUpFromDotIcon", [
  ["path", { d: "m5 9 7-7 7 7", key: "1hw5ic" }],
  ["path", { d: "M12 16V2", key: "ywoabb" }],
  ["circle", { cx: "12", cy: "21", r: "1", key: "o0uj5v" }]
]);

export { ArrowUpFromDot as default };
//# sourceMappingURL=arrow-up-from-dot.js.map
