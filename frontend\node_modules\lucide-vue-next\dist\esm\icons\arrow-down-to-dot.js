/**
 * lucide-vue-next v0.292.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const ArrowDownToDot = createLucideIcon("ArrowDownToDotIcon", [
  ["path", { d: "M12 2v14", key: "jyx4ut" }],
  ["path", { d: "m19 9-7 7-7-7", key: "1oe3oy" }],
  ["circle", { cx: "12", cy: "21", r: "1", key: "o0uj5v" }]
]);

export { ArrowDownToDot as default };
//# sourceMappingURL=arrow-down-to-dot.js.map
