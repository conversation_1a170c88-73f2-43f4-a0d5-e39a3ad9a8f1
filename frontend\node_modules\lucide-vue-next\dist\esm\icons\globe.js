/**
 * lucide-vue-next v0.292.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const Globe = createLucideIcon("GlobeIcon", [
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
  [
    "path",
    { d: "M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20", key: "13o1zl" }
  ],
  ["path", { d: "M2 12h20", key: "9i4pu4" }]
]);

export { Globe as default };
//# sourceMappingURL=globe.js.map
