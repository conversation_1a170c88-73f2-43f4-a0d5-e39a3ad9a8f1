"""Conversation service for managing chat conversations."""

from typing import List, Optional
from sqlalchemy.orm import Session

from models.conversation import Conversation
from crud.conversation_dao import ConversationDao


class ConversationService:
    """Service for handling conversation operations."""

    def get_user_conversations(self, db: Session, username: str) -> List[Conversation]:
        """
        获取用户的所有会话

        Args:
            db: Database session
            username: 用户名

        Returns:
            List[Conversation]: 用户的会话列表
        """
        return ConversationDao.get_by_username(db, username)

    def create_conversation(
        self, db: Session, username: str, title: str = "新的对话"
    ) -> Conversation:
        """
        为用户创建新会话

        Args:
            db: Database session
            username: 用户名
            title: 会话标题

        Returns:
            Conversation: 创建的会话对象
        """
        return ConversationDao.create(db, username, title)

    def get_conversation_by_id(
        self, db: Session, conversation_id: int, username: str
    ) -> Optional[Conversation]:
        """
        获取指定ID的会话（需验证所有权）

        Args:
            db: Database session
            conversation_id: 会话ID
            username: 用户名

        Returns:
            Optional[Conversation]: 会话对象或None
        """
        return ConversationDao.get_by_id(db, conversation_id, username)

    def delete_conversation(
        self, db: Session, conversation_id: int, username: str
    ) -> bool:
        """
        删除会话及其所有消息

        Args:
            db: Database session
            conversation_id: 会话ID
            username: 用户名

        Returns:
            bool: 删除成功返回True，否则返回False
        """
        conversation = self.get_conversation_by_id(db, conversation_id, username)
        if not conversation:
            return False

        ConversationDao.delete(db, conversation)
        return True
