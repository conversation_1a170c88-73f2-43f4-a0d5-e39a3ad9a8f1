import os
from typing import List, Dict, Optional
from datetime import datetime, timezone, timedelta
from pymilvus import (
    connections,
    Collection,
    DataType,
    Function,
    WeightedRanker,
    AnnSearchRequest,
)
from openai import OpenAI
import time
import requests
import traceback
import pandas as pd


class milvus_service:
    """向量存储管理类"""

    def __init__(
        self,
        milvus_url: str,
        milvus_user: str = "",
        milvus_password: str = "",
        db_name: str = "default",
        collection_name: str = "",
        chunk_strategy: str = "custom",
        embedding_model: str = "bge-m3",
        rerank_url: str = "",
        openai_url: str = "",
        openai_api_key: str = "",
    ):
        """
        初始化向量存储

        Args:
            milvus_url: 向量数据库URL
            milvus_user: 向量数据库用户名
            milvus_password: 向量数据库密码
            collection_name: 向量数据库集合名称
            collection_description: 向量数据库集合描述
            chunk_strategy: 分割策略, 默认"custom",
                可选值为 "custom" / "parent_child" / "parent_child_markdown"
                当为"custom"时, 只启用父级文本分割策略,
                当为"parent_child"时, 启用父级和子级文本分割策略,
                当为"parent_child_markdown"时, 启用父级和子级文本分割策略, 并且按照Markdown标题层级进行分割
            embedding_model: 嵌入模型, 默认使用bge-m3
        """
        self.chunk_is_parent = "parent_child" in chunk_strategy
        self.collection_name = collection_name
        self.embedding_model = embedding_model
        self.rerank_url = rerank_url

        # 初始化OpenAI客户端
        self.client_openai = OpenAI(api_key=openai_api_key, base_url=openai_url)

        # 连接Milvus
        connections.connect(
            db_name=db_name, uri=milvus_url, user=milvus_user, password=milvus_password
        )

        # 创建或获取collection
        self.collection = Collection(self.collection_name)
        self.collection.load()
        self.collection_fields = self.get_collection_fields()

    def get_embeddings(
        self, texts: List[str], max_retries: int = 3, retry_delay: float = 3.0
    ) -> List[List[float]]:
        """
        获取文本的嵌入向量，添加重试机制

        Args:
            texts: 文本列表
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间(秒)

        Returns:
            嵌入向量列表
        """
        for attempt in range(max_retries):
            try:
                response = self.client_openai.embeddings.create(
                    model=self.embedding_model, input=texts
                )
                return [item.embedding for item in response.data]
            except Exception as e:
                if "429" in str(e) and attempt < max_retries - 1:
                    time.sleep(retry_delay * (2**attempt))  # 指数退避
                    continue
                raise

    def build_filter_expr(self, filter_conditions: Dict = None) -> Optional[str]:
        """
        构建Milvus过滤表达式

        Args:
            filter_conditions: 元数据过滤条件字典

        Example:
            filter_conditions = {
                'push_datetime': {
                    '$gte': '2024-01-01 00:00:00',
                    '$lte': '2024-01-01 23:59:59'
                }
            }

        Returns:
            构建好的过滤表达式字符串，如果没有过滤条件则返回None
        """
        if not filter_conditions:
            return None

        conditions = []
        for key, value in filter_conditions.items():
            if isinstance(value, dict):
                # 处理范围查询
                for op, val in value.items():
                    if key == "push_datetime" and isinstance(val, str):
                        # 解析北京时间字符串为datetime对象
                        dt = datetime.strptime(val, "%Y-%m-%d %H:%M:%S")
                        # 设置为北京时区
                        dt = dt.replace(tzinfo=timezone(timedelta(hours=8)))
                        # 转换为UTC时间戳
                        val = int(dt.astimezone(timezone.utc).timestamp())

                    if op == "$gte":
                        conditions.append(f"{key} >= {val}")
                    elif op == "$lte":
                        conditions.append(f"{key} <= {val}")
                    elif op == "$eq":
                        conditions.append(f"{key} == {val}")
                    elif op == "$ne":
                        conditions.append(f"{key} != {val}")
                    elif op == "$in":
                        val_str = ",".join(
                            [f"'{v}'" if isinstance(v, str) else str(v) for v in val]
                        )
                        conditions.append(f"{key} in [{val_str}]")
                    elif op == "$nin":
                        val_str = ",".join(
                            [f"'{v}'" if isinstance(v, str) else str(v) for v in val]
                        )
                        conditions.append(f"{key} not in [{val_str}]")
            else:
                # 处理等值查询
                if isinstance(value, str):
                    conditions.append(f"{key} == '{value}'")
                else:
                    conditions.append(f"{key} == {value}")

        return " and ".join(conditions) if conditions else None

    def get_collection_fields(self, exclude_vector_fields: bool = True) -> List[str]:
        """
        获取集合中的所有字段名

        Args:
            exclude_vector_fields: 是否排除向量字段（embedding和sparse_vector），默认为True

        Returns:
            字段名列表
        """
        try:
            # 获取所有字段
            schema = self.collection.schema
            fields = []

            # 遍历所有字段
            for field in schema.fields:
                # 排除主键字段和向量字段
                # if field.is_primary:
                #     continue
                if exclude_vector_fields and field.dtype in [
                    DataType.FLOAT_VECTOR,
                    DataType.SPARSE_FLOAT_VECTOR,
                ]:
                    continue
                fields.append(field.name)

            return fields

        except Exception as e:
            # 返回默认字段列表
            return [
                "document",
                "version",
                "class_name",
                "filename",
                "content",
                "push_datetime",
                "file_hash",
                "is_parent",
                "parent_id",
                "child_id",
                "text_type",
            ]

    def query_by_filter(
        self,
        filter_conditions: Dict = None,
        output_fields: List[str] = None,
        limit: int = None,
    ) -> List[Dict]:
        """
        根据过滤条件从数据库中提取数据，不执行向量搜索

        Args:
            filter_conditions: 过滤条件，例如 {"file_hash": "xxx"} 或 {"file_hash": {"$in": ["hash1", "hash2"]}}
            output_fields: 需要返回的字段列表，如果为None则返回所有字段
            limit: 返回结果的最大数量，如果为None则返回所有结果

        Returns:
            符合条件的文档列表
        """
        try:
            # 构建过滤表达式
            expr = self.build_filter_expr(filter_conditions)

            # 如果没有指定输出字段，则获取所有非向量字段
            if output_fields is None:
                output_fields = self.collection_fields

            # 执行查询
            results = self.collection.query(
                expr=expr, output_fields=output_fields, limit=limit
            )

            # 格式化结果
            documents = []
            for result in results:
                metadata = {
                    field: result.get(field)
                    for field in output_fields
                    if field != "document"  # document字段单独处理
                }

                documents.append(
                    {"document": result.get("document"), "metadata": metadata}
                )

            return documents

        except Exception as e:
            traceback.print_exc()
            return []

    def search_semantic(
        self,
        query: str,
        k: int = 5,
        filter_conditions: Dict = None,
        min_score: float = 0,
        search_parent: bool = False,
        **args,
    ) -> List[Dict]:
        """
        执行语义搜索

        Args:
            query: 查询文本
            k: 返回结果数量
            filter_conditions: 元数据过滤条件
            min_score: 最小匹配分数阈值
            search_parent: 是否搜索父级文章
        Returns:
            相关文档列表
        """
        # 获取查询文本的嵌入向量
        query_embedding = self.get_embeddings([query])[0]
        fix_filter_conditions = filter_conditions.copy() if filter_conditions else {}
        if self.chunk_is_parent:
            if search_parent:
                fix_filter_conditions["text_type"] = "parent"
            else:
                fix_filter_conditions["text_type"] = "child"

        # 准备搜索参数
        search_params = {
            "metric_type": "IP",
            "params": {
                "M": 16,  # 每个节点的邻居数
                "efConstruction": 256,  # 构建索引时考虑的邻居数
            },
        }

        # 构建过滤表达式
        expr = self.build_filter_expr(fix_filter_conditions)

        # 获取输出字段
        output_fields = self.collection_fields

        # 执行搜索
        results = self.collection.search(
            data=[query_embedding],
            anns_field="embedding",
            param=search_params,
            limit=k,
            expr=expr,
            output_fields=output_fields,
        )

        # 格式化结果
        documents = []
        for hits in results:
            for hit in hits:
                if hit.score >= min_score:
                    metadata = {
                        field: hit.entity.get(field)
                        for field in output_fields
                        if field != "document"  # document字段单独处理
                    }

                    documents.append(
                        {
                            "document": hit.entity.get("document"),
                            "metadata": metadata,
                            "score": hit.score,
                            "search_type": "semantic",
                        }
                    )

        return documents

    def search_keyword(
        self,
        query: str,
        k: int = 5,
        filter_conditions: Dict = None,
        min_score: float = 0,
        search_parent: bool = False,
        **args,
    ) -> List[Dict]:
        """
        使用Milvus原生BM25执行关键词搜索

        Args:
            query: 查询文本
            k: 返回结果数量
            filter_conditions: 元数据过滤条件
            min_score: 最小匹配分数阈值
            search_parent: 是否搜索父级文章
        Returns:
            相关文档列表
        """
        fix_filter_conditions = filter_conditions.copy() if filter_conditions else {}
        if self.chunk_is_parent:
            if search_parent:
                fix_filter_conditions["text_type"] = "parent"
            else:
                fix_filter_conditions["text_type"] = "child"
        try:
            # 构建过滤表达式
            expr = self.build_filter_expr(fix_filter_conditions)

            # 获取输出字段
            output_fields = self.collection_fields

            # 执行BM25搜索
            search_params = {"metric_type": "BM25"}
            results = self.collection.search(
                data=[query],
                anns_field="sparse_vector",
                param=search_params,
                limit=k,
                expr=expr,
                output_fields=output_fields,
            )

            # 格式化结果
            documents = []
            for hits in results:
                for hit in hits:
                    if hit.score >= min_score:
                        metadata = {
                            field: hit.entity.get(field)
                            for field in output_fields
                            if field != "document"  # document字段单独处理
                        }

                        documents.append(
                            {
                                "document": hit.entity.get("document"),
                                "metadata": metadata,
                                "score": hit.score,
                                "search_type": "keyword",
                            }
                        )

            return documents

        except Exception as e:
            return []

    def search_hybrid(
        self,
        query: str,
        k: int = 5,
        filter_conditions: Dict = None,
        semantic_weight: float = 0.7,
        min_score: float = 0,
        search_parent: bool = False,
        **args,
    ) -> List[Dict]:
        """
        执行混合搜索，使用Milvus的WeightedRanker进行语义向量和稀疏向量的混合搜索

        Args:
            query: 查询文本
            k: 返回结果数量
            filter_conditions: 元数据过滤条件
            semantic_weight: 语义搜索权重（范围0-1）
            min_score: 最小匹配分数阈值
            search_parent: 是否搜索父级文章
        Returns:
            相关文档列表
        """
        fix_filter_conditions = filter_conditions.copy() if filter_conditions else {}
        if self.chunk_is_parent:
            if search_parent:
                fix_filter_conditions["text_type"] = "parent"
            else:
                fix_filter_conditions["text_type"] = "child"
        try:
            # 获取查询文本的嵌入向量
            query_embedding = self.get_embeddings([query])[0]

            # 构建过滤表达式
            expr = self.build_filter_expr(fix_filter_conditions)

            # 获取输出字段
            output_fields = self.collection_fields

            # 准备搜索参数
            dense_search_params = {
                "metric_type": "IP",
                "params": {
                    "M": 16,  # 每个节点的邻居数
                    "efConstruction": 256,  # 构建索引时考虑的邻居数
                },
            }
            sparse_search_params = {"metric_type": "BM25"}

            # 构建密集向量搜索请求
            dense_req = AnnSearchRequest(
                data=[query_embedding],
                anns_field="embedding",
                param=dense_search_params,
                limit=k,
                expr=expr,
            )

            # 构建稀疏向量搜索请求
            sparse_req = AnnSearchRequest(
                data=[query],
                anns_field="sparse_vector",
                param=sparse_search_params,
                limit=k,
                expr=expr,
            )

            # 设置权重
            keyword_weight = 1 - semantic_weight
            reranker = WeightedRanker(keyword_weight, semantic_weight)

            # 执行混合搜索
            results = self.collection.hybrid_search(
                reqs=[sparse_req, dense_req],  # 搜索请求列表
                rerank=reranker,  # 重排序器
                limit=k,  # 返回结果数量
                output_fields=output_fields,
            )

            # 格式化结果
            documents = []
            for hits in results:
                for hit in hits:
                    if hit.score >= min_score:
                        metadata = {
                            field: hit.entity.get(field)
                            for field in output_fields
                            if field != "document"  # document字段单独处理
                        }

                        documents.append(
                            {
                                "document": hit.entity.get("document"),
                                "metadata": metadata,
                                "score": hit.score,
                                "search_type": "hybrid",
                                "scores": {
                                    "semantic": hit.score * semantic_weight,
                                    "keyword": hit.score * keyword_weight,
                                    "final": hit.score,
                                    "weights": {
                                        "semantic": semantic_weight,
                                        "keyword": keyword_weight,
                                    },
                                },
                            }
                        )

            return documents

        except Exception as e:
            traceback.print_exc()
            return []

    def _search_parent_child(self, func: Function, **kwargs) -> List[Dict]:
        """
        对子级文章执行关键词搜索，并返回父级文章以及命中的子级文章

        Args:
            func: 搜索函数，可以是search_keyword、search_semantic、search_hybrid
            **kwargs: 搜索参数
        Returns:
            包含父级和子级文章的搜索结果列表
        """
        try:
            sort_by = kwargs.get("sort_by", "total_score")
            k = kwargs.get("k", 5)
            filter_conditions = kwargs.get("filter_conditions", {})
            # 1. 首先搜索子文章
            child_filter_conditions = (
                filter_conditions.copy() if filter_conditions else {}
            )
            child_filter_conditions["text_type"] = "child"
            kwargs["filter_conditions"] = child_filter_conditions

            if func == self.search_keyword:
                search_type = "keyword_parent_child"
            elif func == self.search_semantic:
                search_type = "semantic_parent_child"
            elif func == self.search_hybrid:
                search_type = "hybrid_parent_child"
            else:
                search_type = "custom_parent_child"
            # 执行关键词搜索
            child_documents = func(**kwargs)

            if not child_documents:
                return []

            # 2. 按parent_id分组并计算统计信息
            parent_ids_stats = {}
            for item in child_documents:
                parent_id = item["metadata"]["parent_id"]
                if parent_id not in parent_ids_stats:
                    parent_ids_stats[parent_id] = {
                        "score_sum": 0,
                        "hit_count": 0,
                        "child_documents": [],
                        "max_score": 0,
                    }

                stats = parent_ids_stats[parent_id]
                stats["score_sum"] += item["score"]
                stats["hit_count"] += 1
                # 简化子文档格式，只保留文档内容和分数
                stats["child_documents"].append(
                    {"document": item["document"], "score": item["score"]}
                )
                stats["max_score"] = max(stats["max_score"], item["score"])

            # 3. 查找父文档信息
            parent_filter_conditions = {
                "parent_id": {"$in": list(parent_ids_stats.keys())},
                "text_type": "parent",
            }

            # 获取父文档
            parent_documents = self.query_by_filter(
                filter_conditions=parent_filter_conditions,
                output_fields=self.collection_fields,
            )

            # 4. 组织最终结果
            results = []
            for parent_doc in parent_documents:
                parent_id = parent_doc["metadata"]["parent_id"]
                stats = parent_ids_stats[parent_id]

                # 计算平均分数和其他统计信息
                avg_score = stats["score_sum"] / stats["hit_count"]

                # 构建结果对象
                result = {
                    "parent_document": parent_doc["document"],
                    "parent_metadata": parent_doc["metadata"],
                    "search_stats": {
                        "avg_score": avg_score,
                        "max_score": stats["max_score"],
                        "hit_count": stats["hit_count"],
                        "total_score": stats["score_sum"],
                    },
                    "child_hits": sorted(
                        stats["child_documents"], key=lambda x: x["score"], reverse=True
                    ),
                    "search_type": search_type,
                }
                results.append(result)

            # 5. 按指定方式排序
            results.sort(key=lambda x: x["search_stats"][sort_by], reverse=True)

            # 6. 如果指定了k，限制返回结果数量
            if k > 0:
                results = results[:k]

            return results

        except Exception as e:
            traceback.print_exc()
            return []

    def search_keyword_parent_child(
        self,
        query: str,
        k: int = 5,
        filter_conditions: Dict = None,
        min_score: float = 0,
        sort_by: str = "total_score",
    ) -> List[Dict]:
        """
        对子级文章执行关键词搜索，并返回父级文章以及命中的子级文章

        Args:
            query: 查询文本
            k: 返回结果数量
            filter_conditions: 元数据过滤条件
            min_score: 最小匹配分数阈值
            sort_by: 排序方式，可选值为'total_score'(总分)、'max_score'(最高分)、'avg_score'(平均分)、'hit_count'(命中次数)
        Returns:
            包含父级和子级文章的搜索结果列表
        """
        if not self.chunk_is_parent:
            raise ValueError(
                "当前chunk策略不是parent_child，不能使用search_keyword_parent_child方法"
            )
        return self._search_parent_child(
            func=self.search_keyword,
            query=query,
            k=k,
            filter_conditions=filter_conditions,
            min_score=min_score,
            sort_by=sort_by,
        )

    def search_semantic_parent_child(
        self,
        query: str,
        k: int = 5,
        filter_conditions: Dict = None,
        min_score: float = 0,
        sort_by: str = "total_score",
    ) -> List[Dict]:
        """
        对子级文章执行语义搜索，并返回父级文章以及命中的子级文章

        Args:
            query: 查询文本
            k: 返回结果数量
            filter_conditions: 元数据过滤条件
            min_score: 最小匹配分数阈值
            sort_by: 排序方式，可选值为'total_score'(总分)、'max_score'(最高分)、'avg_score'(平均分)、'hit_count'(命中次数)
        Returns:
            包含父级和子级文章的搜索结果列表
        """
        if not self.chunk_is_parent:
            raise ValueError(
                "当前chunk策略不是parent_child，不能使用search_semantic_parent_child方法"
            )
        return self._search_parent_child(
            func=self.search_semantic,
            query=query,
            k=k,
            filter_conditions=filter_conditions,
            min_score=min_score,
            sort_by=sort_by,
        )

    def search_hybrid_parent_child(
        self,
        query: str,
        k: int = 5,
        filter_conditions: Dict = None,
        semantic_weight: float = 0.5,
        min_score: float = 0,
        sort_by: str = "max_score",
    ) -> List[Dict]:
        """
        对子级文章执行混合搜索，并返回父级文章以及命中的子级文章

        Args:
            query: 查询文本
            k: 返回结果数量
            filter_conditions: 元数据过滤条件
            min_score: 最小匹配分数阈值
            sort_by: 排序方式，可选值为'total_score'(总分)、'max_score'(最高分)、'avg_score'(平均分)、'hit_count'(命中次数)
        Returns:
            包含父级和子级文章的搜索结果列表
        """
        if not self.chunk_is_parent:
            raise ValueError(
                "当前chunk策略不是parent_child，不能使用search_hybrid_parent_child方法"
            )
        return self._search_parent_child(
            func=self.search_hybrid,
            query=query,
            k=k,
            filter_conditions=filter_conditions,
            semantic_weight=semantic_weight,
            min_score=min_score,
            sort_by=sort_by,
        )

    def rerank(
        self,
        query: str,
        documents: List[Dict],
        top_k: int = None,
        rerank_threshold: float = 0.0,
        rerank_url: str = "",
        model: str = "bge-reranker-v2-m3",
    ) -> List[Dict]:
        """
        使用reranker模型对搜索结果进行重排序

        Args:
            query: 查询文本
            documents: 搜索结果列表
            top_k: 返回结果数量，如果为None则返回所有结果
            rerank_threshold: 重排序阈值，只返回分数高于此阈值的结果
            model: 重排序模型，默认使用bge-reranker-v2-m3
        """
        documents = documents.copy()
        headers = {"accept": "application/json", "Content-Type": "application/json"}
        if isinstance(documents, list) and isinstance(documents[0], dict):
            documents_text = [x["document"] for x in documents]
        else:
            documents_text = documents
            documents = [{"document": x} for x in documents]
        data = {"model": model, "query": query, "documents": documents_text}
        rerank_url = rerank_url if rerank_url else self.rerank_url
        response = requests.post(rerank_url, headers=headers, json=data)
        if response.status_code != 200:
            raise Exception(f"重排序失败: {response.text}")
        ranker_dict = response.json()
        result_list = []
        for temp_row in ranker_dict["results"]:
            rerank_index = temp_row["index"]
            temp_dict = documents[rerank_index]
            temp_dict["original_index"] = rerank_index  # 记录文档在重排序前的原始位置
            temp_dict["relevance_score"] = temp_row["relevance_score"]
            result_list.append(temp_dict)
        if top_k is not None:
            result_list = result_list[:top_k]
        if rerank_threshold > 0:
            result_list = [
                x for x in result_list if x["relevance_score"] >= rerank_threshold
            ]
        # 按照relevance_score排序
        result_list.sort(key=lambda x: x["relevance_score"], reverse=True)
        # 对排名进行输出
        for i, x in enumerate(result_list):
            x["rerank_k"] = i + 1  # 从1开始
        return result_list

    def _search_neighbors(self, func: Function, p: int = 2, **kwargs) -> List[Dict]:
        """
        对子级文章执行搜索，并返回命中的子级文章及其邻近文章

        Args:
            func: 搜索函数，可以是search_keyword、search_semantic、search_hybrid
            p: 返回匹配文档前后各p个邻近文档
            **kwargs: 搜索参数
        Returns:
            包含子级文章及其邻近文章的搜索结果列表，格式为：
            {
                'matches': [  # 匹配到的文档及其邻近文档
                    {
                        'match': {  # 匹配的文档
                            'document': str,
                            'metadata': Dict,
                            'score': float
                        },
                        'context': [  # 匹配文档的前后文档
                            {
                                'document': str,
                                'position': int,  # 相对于匹配文档的位置（-p到p）
                                'is_before': bool  # 是否在匹配文档之前
                            }
                        ]
                    }
                ],
                'search_type': str,
                'search_stats': {
                    'total_matches': int,  # 匹配的文档总数
                    'max_score': float,    # 最高匹配分数
                    'min_score': float,    # 最低匹配分数
                    'avg_score': float     # 平均匹配分数
                }
            }
        """
        try:
            sort_by = kwargs.get("sort_by", "score")  # 默认按单个文档的分数排序
            k = kwargs.get("k", 5)
            filter_conditions = kwargs.get("filter_conditions", {})

            # 1. 首先搜索子文章
            child_filter_conditions = (
                filter_conditions.copy() if filter_conditions else {}
            )
            if self.chunk_is_parent:
                child_filter_conditions["text_type"] = "child"
            else:
                child_filter_conditions["text_type"] = "parent"
            kwargs["filter_conditions"] = child_filter_conditions

            if func == self.search_keyword:
                search_type = "keyword_neighbors"
            elif func == self.search_semantic:
                search_type = "semantic_neighbors"
            elif func == self.search_hybrid:
                search_type = "hybrid_neighbors"
            else:
                search_type = "custom_neighbors"

            # 执行搜索
            child_documents = func(**kwargs)

            if not child_documents:
                return {
                    "matches": [],
                    "search_type": search_type,
                    "search_stats": {
                        "total_matches": 0,
                        "max_score": 0,
                        "min_score": 0,
                        "avg_score": 0,
                    },
                }

            # 2. 收集所有匹配文档的信息
            matches = []
            scores = []
            for item in child_documents:
                matches.append(
                    {
                        "document": item["document"],
                        "metadata": item["metadata"],
                        "score": item["score"],
                    }
                )
                scores.append(item["score"])

            # 3. 按分数排序匹配文档
            matches.sort(key=lambda x: x["score"], reverse=True)

            # 4. 为每个匹配文档找到其上下文
            matches_with_context = []
            for match in matches[:k]:  # 只处理前k个匹配文档
                # 获取该文档所在文件的所有子文档
                child_filter_conditions = {
                    "file_hash": match["metadata"]["file_hash"],
                    "text_type": "child",
                }

                all_child_docs = self.query_by_filter(
                    filter_conditions=child_filter_conditions,
                    output_fields=self.collection_fields,
                )

                # 按id排序所有子文档
                all_child_docs.sort(key=lambda x: x["metadata"]["id"])

                # 找到匹配文档的位置
                match_pos = next(
                    i
                    for i, doc in enumerate(all_child_docs)
                    if doc["metadata"]["id"] == match["metadata"]["id"]
                )

                # 获取前后文档
                context = []
                # 前p个文档
                for i in range(max(0, match_pos - p), match_pos):
                    doc = all_child_docs[i]
                    context.append(
                        {
                            "document": doc["document"],
                            "position": i - match_pos,  # 负数表示在匹配文档之前
                            "is_before": True,
                        }
                    )

                # 后p个文档
                for i in range(
                    match_pos + 1, min(len(all_child_docs), match_pos + p + 1)
                ):
                    doc = all_child_docs[i]
                    context.append(
                        {
                            "document": doc["document"],
                            "position": i - match_pos,  # 正数表示在匹配文档之后
                            "is_before": False,
                        }
                    )

                matches_with_context.append({"match": match, "context": context})

            # 5. 计算统计信息
            result = {
                "matches": matches_with_context,
                "search_type": search_type,
                "search_stats": {
                    "total_matches": len(matches),
                    "max_score": max(scores),
                    "min_score": min(scores),
                    "avg_score": sum(scores) / len(scores),
                },
            }

            return result

        except Exception as e:
            traceback.print_exc()
            return {
                "matches": [],
                "search_type": search_type if "search_type" in locals() else "unknown",
                "search_stats": {
                    "total_matches": 0,
                    "max_score": 0,
                    "min_score": 0,
                    "avg_score": 0,
                },
            }

    def search_keyword_neighbors(
        self,
        query: str,
        k: int = 5,
        p: int = 2,
        filter_conditions: Dict = None,
        min_score: float = 0,
        sort_by: str = "total_score",
    ) -> List[Dict]:
        """
        对文章执行关键词搜索，并返回命中的子级文章及其邻近文章

        Args:
            query: 查询文本
            k: 返回结果数量
            p: 返回匹配文档前后各p个邻近文档
            filter_conditions: 元数据过滤条件
            min_score: 最小匹配分数阈值
            sort_by: 排序方式，可选值为'total_score'(总分)、'max_score'(最高分)、'avg_score'(平均分)、'hit_count'(命中次数)
        Returns:
            包含子级文章及其邻近文章的搜索结果列表，格式为：
            {
                'matches': [  # 匹配到的文档及其邻近文档
                    {
                        'match': {  # 匹配的文档
                            'document': str,
                            'metadata': Dict,
                            'score': float
                        },
                        'context': [  # 匹配文档的前后文档
                            {
                                'document': str,
                                'position': int,  # 相对于匹配文档的位置（-p到p）
                                'is_before': bool  # 是否在匹配文档之前
                            }
                        ]
                    }
                ],
                'search_type': str,
                'search_stats': {
                    'total_matches': int,  # 匹配的文档总数
                    'max_score': float,    # 最高匹配分数
                    'min_score': float,    # 最低匹配分数
                    'avg_score': float     # 平均匹配分数
                }
            }
        """
        return self._search_neighbors(
            func=self.search_keyword,
            query=query,
            k=k,
            p=p,
            filter_conditions=filter_conditions,
            min_score=min_score,
            sort_by=sort_by,
        )

    def search_semantic_neighbors(
        self,
        query: str,
        k: int = 5,
        p: int = 2,
        filter_conditions: Dict = None,
        min_score: float = 0,
        sort_by: str = "total_score",
    ) -> List[Dict]:
        """
        对文章执行语义搜索，并返回命中的子级文章及其邻近文章

        Args:
            query: 查询文本
            k: 返回结果数量
            p: 返回匹配文档前后各p个邻近文档
            filter_conditions: 元数据过滤条件
            min_score: 最小匹配分数阈值
            sort_by: 排序方式，可选值为'total_score'(总分)、'max_score'(最高分)、'avg_score'(平均分)、'hit_count'(命中次数)
        Returns:
            包含子级文章及其邻近文章的搜索结果列表，格式为：
            {
                'matches': [  # 匹配到的文档及其邻近文档
                    {
                        'match': {  # 匹配的文档
                            'document': str,
                            'metadata': Dict,
                            'score': float
                        },
                        'context': [  # 匹配文档的前后文档
                            {
                                'document': str,
                                'position': int,  # 相对于匹配文档的位置（-p到p）
                                'is_before': bool  # 是否在匹配文档之前
                            }
                        ]
                    }
                ],
                'search_type': str,
                'search_stats': {
                    'total_matches': int,  # 匹配的文档总数
                    'max_score': float,    # 最高匹配分数
                    'min_score': float,    # 最低匹配分数
                    'avg_score': float     # 平均匹配分数
                }
            }
        """
        return self._search_neighbors(
            func=self.search_semantic,
            query=query,
            k=k,
            p=p,
            filter_conditions=filter_conditions,
            min_score=min_score,
            sort_by=sort_by,
        )

    def search_hybrid_neighbors(
        self,
        query: str,
        k: int = 5,
        p: int = 2,
        filter_conditions: Dict = None,
        semantic_weight: float = 0.5,
        min_score: float = 0,
        sort_by: str = "total_score",
    ) -> List[Dict]:
        """
        对文章执行混合搜索，并返回命中的子级文章及其邻近文章

        Args:
            query: 查询文本
            k: 返回结果数量
            p: 返回匹配文档前后各p个邻近文档
            filter_conditions: 元数据过滤条件
            semantic_weight: 语义搜索权重
            min_score: 最小匹配分数阈值
            sort_by: 排序方式，可选值为'total_score'(总分)、'max_score'(最高分)、'avg_score'(平均分)、'hit_count'(命中次数)
        Returns:
            包含子级文章及其邻近文章的搜索结果列表，格式为：
            {
                'matches': [  # 匹配到的文档及其邻近文档
                    {
                        'match': {  # 匹配的文档
                            'document': str,
                            'metadata': Dict,
                            'score': float
                        },
                        'context': [  # 匹配文档的前后文档
                            {
                                'document': str,
                                'position': int,  # 相对于匹配文档的位置（-p到p）
                                'is_before': bool  # 是否在匹配文档之前
                            }
                        ]
                    }
                ],
                'search_type': str,
                'search_stats': {
                    'total_matches': int,  # 匹配的文档总数
                    'max_score': float,    # 最高匹配分数
                    'min_score': float,    # 最低匹配分数
                    'avg_score': float     # 平均匹配分数
                }
            }
        """
        return self._search_neighbors(
            func=self.search_hybrid,
            query=query,
            k=k,
            p=p,
            filter_conditions=filter_conditions,
            semantic_weight=semantic_weight,
            min_score=min_score,
            sort_by=sort_by,
        )

    def _time_weight(self, df, time_weight_ratio=0.2):
        """
        时间加权得分
        """
        df["time_rank"] = df["push_datetime"].rank()
        df["time_weight"] = df["time_rank"] / df["time_rank"].sum()

        # 时间加权占据20%比重
        df["score_fin"] = df["score"] * (
            time_weight_ratio * df["time_weight"] + (1 - time_weight_ratio)
        )
        return df

    def parent_child_docs_time_weight(self, docs, top_k=0, time_weight_ratio=0.2):
        """
        父级文章及其子级文章时间加权得分
        Args:
            docs: 父级文章及其子级文章列表
            top_k: 返回结果数量, 0表示返回所有
            time_weight_ratio: 时间加权比重
        Returns:
            pd.DataFrame: 父级文章及其子级文章时间加权得分
        """
        temp_df = pd.DataFrame(docs)
        temp_df["push_datetime"] = temp_df["parent_metadata"].apply(
            lambda x: x["push_datetime"]
        )
        temp_df["score"] = temp_df["search_stats"].apply(lambda x: x["max_score"])
        temp_df = self._time_weight(temp_df, time_weight_ratio).sort_values(
            "score_fin", ascending=False
        )
        # 取前top_k
        if top_k > 0:
            temp_df = temp_df.iloc[:top_k]
        return temp_df

    def docs_time_weight(self, docs, top_k, time_weight_ratio=0.2):
        """
        文章时间加权得分
        """
        temp_df = pd.DataFrame(docs)
        temp_df["push_datetime"] = temp_df["metadata"].apply(
            lambda x: x["push_datetime"]
        )
        # 取前五
        temp_df = (
            self._time_weight(temp_df, time_weight_ratio)
            .sort_values("score_fin", ascending=False)
            .iloc[:top_k]
        )
        return temp_df

    def timestamp_to_date(self, timestamp):
        """
        将时间戳转换为日期字符串

        参数:
            timestamp (int): Unix时间戳（秒）

        返回:
            str: 格式化的日期字符串，格式为YYYY-MM-DD HH:MM:SS
        """
        dt_object = datetime.fromtimestamp(timestamp)
        return dt_object.strftime("%Y-%m-%d %H:%M:%S")
