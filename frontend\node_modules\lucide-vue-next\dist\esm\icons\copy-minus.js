/**
 * lucide-vue-next v0.292.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const CopyMinus = createLucideIcon("CopyMinusIcon", [
  ["line", { x1: "12", x2: "18", y1: "15", y2: "15", key: "1nscbv" }],
  [
    "rect",
    {
      width: "14",
      height: "14",
      x: "8",
      y: "8",
      rx: "2",
      ry: "2",
      key: "17jyea"
    }
  ],
  [
    "path",
    {
      d: "M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",
      key: "zix9uf"
    }
  ]
]);

export { CopyMinus as default };
//# sourceMappingURL=copy-minus.js.map
