/**
 * lucide-vue-next v0.292.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const AlignHorizontalJustifyCenter = createLucideIcon(
  "AlignHorizontalJustifyCenterIcon",
  [
    [
      "rect",
      { width: "6", height: "14", x: "2", y: "5", rx: "2", key: "dy24zr" }
    ],
    [
      "rect",
      { width: "6", height: "10", x: "16", y: "7", rx: "2", key: "13zkjt" }
    ],
    ["path", { d: "M12 2v20", key: "t6zp3m" }]
  ]
);

export { AlignHorizontalJustifyCenter as default };
//# sourceMappingURL=align-horizontal-justify-center.js.map
