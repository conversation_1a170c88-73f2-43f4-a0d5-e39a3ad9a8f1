/**
 * lucide-vue-next v0.292.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const StretchHorizontal = createLucideIcon("StretchHorizontalIcon", [
  [
    "rect",
    { width: "20", height: "6", x: "2", y: "4", rx: "2", key: "qdearl" }
  ],
  [
    "rect",
    { width: "20", height: "6", x: "2", y: "14", rx: "2", key: "1xrn6j" }
  ]
]);

export { StretchHorizontal as default };
//# sourceMappingURL=stretch-horizontal.js.map
