/**
 * lucide-vue-next v0.292.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const AlignHorizontalJustifyStart = createLucideIcon(
  "AlignHorizontalJustifyStartIcon",
  [
    [
      "rect",
      { width: "6", height: "14", x: "6", y: "5", rx: "2", key: "hsirpf" }
    ],
    [
      "rect",
      { width: "6", height: "10", x: "16", y: "7", rx: "2", key: "13zkjt" }
    ],
    ["path", { d: "M2 2v20", key: "1ivd8o" }]
  ]
);

export { AlignHorizontalJustifyStart as default };
//# sourceMappingURL=align-horizontal-justify-start.js.map
