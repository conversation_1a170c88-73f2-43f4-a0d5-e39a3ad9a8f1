from sqlalchemy.orm import Session
from typing import List
from models.message import Message


class MessageDao:
    @staticmethod
    def get_by_conversation_id(db: Session, conversation_id: int) -> List[Message]:
        return (
            db.query(Message)
            .filter(Message.conversation_id == conversation_id)
            .order_by(Message.timestamp.asc())
            .all()
        )

    @staticmethod
    def create(db: Session, conversation_id: int, role: str, content: str) -> Message:
        message = Message(conversation_id=conversation_id, role=role, content=content)
        db.add(message)
        db.commit()
        db.refresh(message)
        return message
