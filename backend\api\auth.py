"""Authentication API routes."""

from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status

from config.settings import settings
from schemas.auth import LoginRequest, LoginResponse, UserResponse
from services.auth_service import AuthService
from utils.dependencies import get_current_username

router = APIRouter(prefix="/api", tags=["认证"])
auth_service = AuthService()


@router.post("/login", response_model=LoginResponse, summary="用户登录")
async def login(login_data: LoginRequest):
    """用户登录接口"""
    # 1. 验证用户凭据
    is_authenticated = auth_service.authenticate_user(
        login_data.username, login_data.password
    )
    
    if not is_authenticated:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误，或LDAP服务不可用"
        )
    
    # 2. 生成访问令牌
    access_token_expires = timedelta(
        minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
    )
    access_token = auth_service.create_access_token(
        username=login_data.username,
        expires_delta=access_token_expires
    )
    
    # 3. 返回令牌和用户信息
    return LoginResponse(
        access_token=access_token,
        token_type="bearer",
        user=UserResponse(username=login_data.username)
    )


@router.get("/user/me", response_model=UserResponse, summary="获取当前用户信息")
async def get_current_user_info(
    current_username: str = Depends(get_current_username)
):
    """获取当前用户信息"""
    return UserResponse(username=current_username)
