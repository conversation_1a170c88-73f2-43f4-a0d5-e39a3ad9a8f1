/**
 * lucide-vue-next v0.292.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const AlignHorizontalSpaceAround = createLucideIcon(
  "AlignHorizontalSpaceAroundIcon",
  [
    [
      "rect",
      { width: "6", height: "10", x: "9", y: "7", rx: "2", key: "yn7j0q" }
    ],
    ["path", { d: "M4 22V2", key: "tsjzd3" }],
    ["path", { d: "M20 22V2", key: "1bnhr8" }]
  ]
);

export { AlignHorizontalSpaceAround as default };
//# sourceMappingURL=align-horizontal-space-around.js.map
