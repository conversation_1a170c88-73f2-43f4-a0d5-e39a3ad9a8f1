/**
 * lucide-vue-next v0.292.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const HardDriveUpload = createLucideIcon("HardDriveUploadIcon", [
  ["path", { d: "m16 6-4-4-4 4", key: "13yo43" }],
  ["path", { d: "M12 2v8", key: "1q4o3n" }],
  [
    "rect",
    { width: "20", height: "8", x: "2", y: "14", rx: "2", key: "w68u3i" }
  ],
  ["path", { d: "M6 18h.01", key: "uhywen" }],
  ["path", { d: "M10 18h.01", key: "h775k" }]
]);

export { HardDriveUpload as default };
//# sourceMappingURL=hard-drive-upload.js.map
