{"version": 3, "file": "no-unsafe-call.js", "sourceRoot": "", "sources": ["../../src/rules/no-unsafe-call.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,sDAAwC;AAExC,kCAMiB;AAQjB,kBAAe,IAAA,iBAAU,EAAiB;IACxC,IAAI,EAAE,gBAAgB;IACtB,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,0CAA0C;YACvD,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,UAAU,EAAE,sCAAsC;YAClD,cAAc,EAAE;gBACd,gEAAgE;gBAChE,2HAA2H;aAC5H,CAAC,IAAI,CAAC,IAAI,CAAC;YACZ,SAAS,EAAE,2CAA2C;YACtD,iBAAiB,EAAE,gCAAgC;SACpD;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAC9D,MAAM,gBAAgB,GAAG,OAAO,CAAC,6BAA6B,CAC5D,eAAe,EACf,gBAAgB,CACjB,CAAC;QAEF,SAAS,SAAS,CAChB,IAAmB,EACnB,aAA4B,EAC5B,SAAqB;YAErB,MAAM,IAAI,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAE1D,IAAI,IAAA,oBAAa,EAAC,IAAI,CAAC,EAAE,CAAC;gBACxB,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACtB,gDAAgD;oBAChD,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,IAAI,CAAC,CAAC;oBAC/C,IACE,cAAc;wBACd,IAAA,oBAAa,EACX,IAAA,mCAA4B,EAAC,QAAQ,EAAE,cAAc,CAAC,CACvD,EACD,CAAC;wBACD,SAAS,GAAG,gBAAgB,CAAC;oBAC/B,CAAC;gBACH,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,aAAa;oBACnB,SAAS,EAAE,SAAS;iBACrB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,2BAA2B,CACzB,IAAuC;gBAEvC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;YACtC,CAAC;YACD,aAAa,CAAC,IAAI;gBAChB,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;YAC5C,CAAC;YACD,kCAAkC,CAAC,IAAmB;gBACpD,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,mBAAmB,CAAC,CAAC;YAC7C,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}