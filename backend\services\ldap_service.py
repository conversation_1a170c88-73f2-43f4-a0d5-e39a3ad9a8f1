"""LDAP 认证服务实现"""

from ldap3 import Server, Connection, ALL, core
import logging
from typing import bool


class LDAPService:
    """LDAP 认证服务"""

    def __init__(self, ldap_uri: str, base_dn: str, timeout: int = 3):
        self.ldap_uri = ldap_uri
        self.base_dn = base_dn
        self.timeout = timeout
        self.logger = logging.getLogger(__name__)

    def authenticate_user(self, username: str, password: str) -> bool:
        """
        LDAP 用户认证

        Args:
            username: 用户名
            password: 密码

        Returns:
            bool: 认证成功返回 True，否则返回 False
        """
        user_dn = f"cn={username},{self.base_dn}"
        self.logger.info(f"尝试 LDAP 认证: {user_dn}")

        server = Server(self.ldap_uri, get_info=ALL, connect_timeout=self.timeout)

        try:
            conn = Connection(
                server,
                user=user_dn,
                password=password,
                authentication="SIMPLE",
                auto_bind=True,
            )
            conn.unbind()
            self.logger.info(f"[LDAP认证成功]: {username}")
            return True

        except core.exceptions.LDAPBindError as e:
            self.logger.error(f"[LDAP认证失败]: {username}, {e}")
            return False

        except Exception as e:
            self.logger.error(f"[LDAP连接失败]: {username}, {e}")
            return False
