
## 1. 总体架构

本系统基于 FastAPI + Vue3 实现前后端分离，后端采用流式接口实现 RAG（Retrieval-Augmented Generation）问答链路，向量检索底层集成 Milvus，支持父子分块的混合检索，具备高效的知识检索与问答能力。

## 2. 主要流程

### 2.1 流式问答流程

1. 前端通过 `/api/chat/stream` 接口以 POST 方式发送用户问题（含会话ID与消息内容）。
2. 后端校验用户身份，保存用户消息。
3. 后端调用消息服务 `generate_chat_response`，生成 AI 回复（当前为 TODO，后续集成 langchain RAG）。
4. AI 回复以 SSE（Server-Sent Events）流式返回前端，前端实时展示。
5. 回复内容保存至数据库。

### 2.2 向量检索流程

- 检索服务调用 `milvus_service.search_hybrid_parent_child` 方法，先对子块进行混合检索（语义+关键词），再聚合父块，返回父块及命中的子块，支持多种排序方式（总分、最高分、平均分、命中数）。
- 底层混合检索通过 Milvus 的 WeightedRanker 实现，支持语义权重调节。

## 3. 关键模块与接口

### 3.1 `/api/chat/stream` 接口

- **方法**：POST
- **请求体**（`ChatRequest`）：
  - `conversation_id`: int，会话ID
  - `message`: str，用户消息内容
- **响应**：SSE流，格式为 `data: {"content": "xxx"}`，结束时 `data: [DONE]`
- **主要流程**：
  1. 校验用户身份
  2. 保存用户消息
  3. 生成AI回复（流式）
  4. 保存AI回复
  5. 错误处理

### 3.2 消息服务（`MessageService`）

- `create_user_message`：保存用户消息
- `create_assistant_message`：保存AI回复
- `generate_chat_response`：生成流式回复（当前为 mock，后续集成 langchain RAG）

### 3.3 向量检索服务（`VectorStore_Search`）

- `search_hybrid_parent_child`：
  - 先对子块执行混合检索（`search_hybrid`），再聚合父块，返回父块及命中的子块
  - 支持参数：`query`（查询文本）、`k`（返回数量）、`filter_conditions`（过滤条件）、`semantic_weight`（语义权重）、`min_score`（分数阈值）、`sort_by`（排序方式）
  - 内部流程：
    1. 仅对子块（`text_type=child`）执行混合检索
    2. 聚合命中子块的父块，统计分数、命中数等
    3. 查询父块内容，组装最终结果
    4. 支持多种排序方式

- `search_hybrid`：
  - 语义向量与稀疏向量（BM25）混合检索
  - 通过 WeightedRanker 进行加权重排序
  - 支持分数阈值过滤

## 4. 未来扩展与 TODO

- 消息服务 `generate_chat_response` 需集成 langchain RAG 问答链路，调用 Milvus 检索结果作为上下文，结合大模型生成最终回复。
- 前端需支持 SSE 流式消息的实时展示与错误处理。
- 检索服务可扩展多种分块策略与 rerank 模型。
