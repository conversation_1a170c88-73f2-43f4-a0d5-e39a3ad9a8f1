/**
 * lucide-vue-next v0.292.0 - ISC
 */

import { h } from 'vue';
import defaultAttributes from './defaultAttributes.js';

const toKebabCase = (string) => string.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase();
const createLucideIcon = (iconName, iconNode) => ({ size, strokeWidth = 2, absoluteStrokeWidth, color, ...props }, { attrs, slots }) => {
  return h(
    "svg",
    {
      ...defaultAttributes,
      width: size || defaultAttributes.width,
      height: size || defaultAttributes.height,
      stroke: color || defaultAttributes.stroke,
      "stroke-width": absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,
      ...attrs,
      class: ["lucide", `lucide-${toKebabCase(iconName)}`, attrs?.class || ""],
      ...props
    },
    [
      ...iconNode.map((child) => h(...child)),
      ...slots.default ? [slots.default()] : []
    ]
  );
};

export { createLucideIcon as default, toKebabCase };
//# sourceMappingURL=createLucideIcon.js.map
