/**
 * lucide-vue-next v0.292.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const AlignStartHorizontal = createLucideIcon("AlignStartHorizontalIcon", [
  [
    "rect",
    { width: "6", height: "16", x: "4", y: "6", rx: "2", key: "1n4dg1" }
  ],
  [
    "rect",
    { width: "6", height: "9", x: "14", y: "6", rx: "2", key: "17khns" }
  ],
  ["path", { d: "M22 2H2", key: "fhrpnj" }]
]);

export { AlignStartHorizontal as default };
//# sourceMappingURL=align-start-horizontal.js.map
