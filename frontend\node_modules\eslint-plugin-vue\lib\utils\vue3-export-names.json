["ComputedGetter", "ComputedRef", "ComputedSetter", "CustomRefFactory", "DebuggerEvent", "DebuggerEventExtraInfo", "DebuggerOptions", "DeepReadonly", "EffectScheduler", "EffectScope", "MaybeRef", "MaybeRefOrGetter", "Raw", "Reactive", "ReactiveEffect", "ReactiveEffectOptions", "ReactiveEffectRunner", "ReactiveFlags", "Ref", "ShallowReactive", "ShallowRef", "ShallowUnwrapRef", "ToRef", "ToRefs", "TrackOpTypes", "TriggerOpTypes", "UnwrapNestedRefs", "UnwrapRef", "WatchCallback", "WatchEffect", "WatchHandle", "WatchSource", "WatchStopHandle", "WritableComputedOptions", "WritableComputedRef", "customRef", "effect", "effectScope", "getCurrentScope", "getCurrentWatcher", "isProxy", "isReactive", "is<PERSON><PERSON><PERSON>ly", "isRef", "isShallow", "mark<PERSON>aw", "onScopeDispose", "onWatcherCleanup", "proxyRefs", "reactive", "readonly", "ref", "shallowReactive", "shallowReadonly", "shallowRef", "stop", "toRaw", "toRef", "toRefs", "toValue", "triggerRef", "unref", "camelize", "capitalize", "normalizeClass", "normalizeProps", "normalizeStyle", "toDisplayString", "toHandlerKey", "computed", "Slot", "Slots", "SlotsType", "nextTick", "queuePostFlushCb", "ComponentPropsOptions", "ComponentObjectPropsOptions", "Prop", "PropType", "ExtractPropTypes", "ExtractPublicPropTypes", "ExtractDefaultPropTypes", "defineProps", "DefineProps", "defineEmits", "ComponentTypeEmits", "defineExpose", "defineOptions", "defineSlots", "ModelRef", "defineModel", "with<PERSON><PERSON><PERSON><PERSON>", "useSlots", "useAttrs", "ObjectEmitsOptions", "EmitsOptions", "EmitsToProps", "ShortEmitsToObject", "EmitFn", "DirectiveBinding", "DirectiveHook", "ObjectDirective", "FunctionDirective", "Directive", "DirectiveArguments", "withDirectives", "ComponentCustomProperties", "CreateComponentPublicInstance", "CreateComponentPublicInstanceWithMixins", "ComponentPublicInstance", "SuspenseProps", "Suspense", "SuspenseBoundary", "RootHydrateFunction", "BaseTransitionProps", "TransitionHooks", "TransitionState", "useTransitionState", "BaseTransitionPropsValidators", "BaseTransition", "resolveTransitionHooks", "setTransitionHooks", "getTransitionRawChildren", "<PERSON><PERSON><PERSON>", "Hydration<PERSON><PERSON><PERSON>", "ElementNamespace", "RootRenderFunction", "RendererOptions", "RendererNode", "RendererElement", "<PERSON><PERSON><PERSON><PERSON>", "createHydrationRenderer", "KeepAliveProps", "KeepAlive", "onActivated", "onDeactivated", "onBeforeMount", "onMounted", "onBeforeUpdate", "onUpdated", "onBeforeUnmount", "onUnmounted", "onServerPrefetch", "onRenderTriggered", "onRenderTracked", "onErrorCaptured", "ComponentCustomOptions", "RenderFunction", "ComponentOptionsBase", "RuntimeCompilerOptions", "ComponentOptions", "ComponentOptionsMixin", "ComputedOptions", "MethodOptions", "ComponentProvideOptions", "ComponentInjectOptions", "ComponentOptionsWithoutProps", "ComponentOptionsWithArrayProps", "ComponentOptionsWithObjectProps", "Injection<PERSON>ey", "provide", "inject", "hasInjectionContext", "PublicProps", "DefineComponent", "DefineSetupFnComponent", "defineComponent", "App", "OptionMergeFunction", "AppConfig", "AppContext", "ObjectPlugin", "FunctionPlugin", "Plugin", "CreateAppFunction", "TeleportProps", "Teleport", "resolveComponent", "resolveDynamicComponent", "resolveDirective", "Fragment", "Text", "Comment", "Static", "VNodeTypes", "VNodeRef", "VNodeProps", "VNodeArrayChildren", "VNodeChild", "VNodeNormalizedChildren", "VNode", "openBlock", "setBlockTracking", "createElementBlock", "createBlock", "isVNode", "transformVNodeArgs", "createBaseVNode", "createVNode", "guardReactiveProps", "cloneVNode", "createTextVNode", "createStaticVNode", "createCommentVNode", "mergeProps", "ComponentInstance", "ComponentCustomProps", "GlobalDirectives", "GlobalComponents", "AllowedComponentProps", "FunctionalComponent", "ConcreteComponent", "Component", "SetupContext", "ComponentInternalInstance", "getCurrentInstance", "registerRuntimeCompiler", "isRuntimeOnly", "ComponentCustomElementInterface", "WatchEffectOptions", "WatchOptions", "watchEffect", "watchPostEffect", "watchSyncEffect", "MultiWatchSources", "watch", "HydrationStrategy", "HydrationStrategyFactory", "hydrateOnIdle", "hydrateOnVisible", "hydrateOnMediaQuery", "hydrateOnInteraction", "AsyncComponent<PERSON><PERSON>der", "AsyncComponentOptions", "defineAsyncComponent", "useModel", "useTemplateRef", "useId", "h", "ssrContextKey", "useSSRContext", "ErrorCodes", "callWithErrorHandling", "callWithAsyncErrorHandling", "handleError", "initCustomFormatter", "HMRRuntime", "pushScopeId", "popScopeId", "withScopeId", "withCtx", "renderList", "toHandlers", "renderSlot", "createSlots", "withMemo", "isMemoSame", "LegacyConfig", "CompatVue", "version", "warn", "devtools", "setDevtoolsHook", "DeprecationTypes", "WatchOptionsBase", "createElementVNode", "TransitionProps", "Transition", "TransitionGroupProps", "TransitionGroup", "vShow", "withModifiers", "<PERSON><PERSON><PERSON><PERSON>", "vModelText", "vModelCheckbox", "vModelRadio", "vModelSelect", "vModelDynamic", "VueElementConstructor", "CustomElementOptions", "defineCustomElement", "defineSSRCustomElement", "<PERSON>ue<PERSON>lement", "useHost", "useShadowRoot", "useCssModule", "useCssVars", "CSSProperties", "AriaAttributes", "StyleValue", "HTMLAttributes", "AnchorHTMLAttributes", "AreaHTMLAttributes", "AudioHTMLAttributes", "BaseHTMLAttributes", "BlockquoteHTMLAttributes", "ButtonHTMLAttributes", "CanvasHTMLAttributes", "ColHTMLAttributes", "ColgroupHTMLAttributes", "DataHTMLAttributes", "DetailsHTMLAttributes", "DelHTMLAttributes", "DialogHTMLAttributes", "EmbedHTMLAttributes", "FieldsetHTMLAttributes", "FormHTMLAttributes", "HtmlHTMLAttributes", "IframeHTMLAttributes", "ImgHTMLAttributes", "InsHTMLAttributes", "InputTypeHTMLAttribute", "InputHTMLAttributes", "KeygenHTMLAttributes", "LabelHTMLAttributes", "LiHTMLAttributes", "LinkHTMLAttributes", "MapHTMLAttributes", "MenuHTMLAttributes", "MediaHTMLAttributes", "MetaHTMLAttributes", "MeterHTMLAttributes", "QuoteHTMLAttributes", "ObjectHTMLAttributes", "OlHTMLAttributes", "OptgroupHTMLAttributes", "OptionHTMLAttributes", "OutputHTMLAttributes", "ParamHTMLAttributes", "ProgressHTMLAttributes", "ScriptHTMLAttributes", "SelectHTMLAttributes", "SourceHTMLAttributes", "StyleHTMLAttributes", "TableHTMLAttributes", "TextareaHTMLAttributes", "TdHTMLAttributes", "ThHTMLAttributes", "TimeHTMLAttributes", "TrackHTMLAttributes", "VideoHTMLAttributes", "WebViewHTMLAttributes", "SVGAttributes", "IntrinsicElementAttributes", "Events", "ReservedProps", "NativeElements", "render", "hydrate", "createApp", "createSSRApp", "compileToFunction", "compile"]