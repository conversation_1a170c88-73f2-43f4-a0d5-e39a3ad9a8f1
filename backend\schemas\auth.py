"""Authentication schemas for request/response validation."""

from pydantic import BaseModel


class LoginRequest(BaseModel):
    """Login request schema."""

    username: str
    password: str


class UserResponse(BaseModel):
    """User response schema."""

    username: str


class LoginResponse(BaseModel):
    """Login response schema."""

    access_token: str
    token_type: str
    user: UserResponse
