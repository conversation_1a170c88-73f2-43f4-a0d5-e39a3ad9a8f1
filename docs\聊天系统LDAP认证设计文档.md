# 聊天系统 LDAP 认证设计文档

## 1. 项目概述

### 1.1 项目简介
本项目是一个基于 FastAPI 和 Vue3 的实时聊天系统，集成了 LDAP 用户认证和 JWT 令牌管理。系统采用前后端分离架构，支持多用户对话管理和实时消息流。**用户认证采用无状态设计，不在本地数据库存储用户信息，完全依赖 LDAP 验证和 JWT 令牌管理。**

### 1.2 技术栈
- **后端框架**: FastAPI 0.116+
- **前端框架**: Vue3 + TypeScript
- **数据库**: SQLite (仅存储会话和消息)
- **认证协议**: LDAP + JWT (OAuth2 Password Flow)
- **包管理**: uv + pyproject.toml
- **ORM**: SQLAlchemy 2.0+

### 1.3 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vue3 前端     │    │  FastAPI 后端   │    │  LDAP 服务器    │
│                 │    │                 │    │                 │
│  - 登录页面     │◄──►│  - 认证 API     │◄──►│  - 用户验证     │
│  - 聊天界面     │    │  - 会话管理     │    │  - 权限管理     │
│  - 消息流      │    │  - 消息处理     │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  SQLite 数据库  │
                       │                 │
                       │  - 会话记录     │
                       │  - 消息历史     │
                       └─────────────────┘
```

## 2. 认证系统设计

### 2.1 认证流程概述

#### 2.1.1 用户登录流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端 (Vue3)
    participant A as 后端 API
    participant L as LDAP 服务

    U->>F: 输入用户名和密码
    F->>A: POST /api/login
    A->>L: LDAP 绑定验证
    L-->>A: 验证结果
    
    alt 验证成功
        A->>A: 生成 JWT Token (包含用户名)
        A-->>F: 返回 Token + 用户信息
        F-->>U: 登录成功，跳转聊天页面
    else 验证失败
        A-->>F: 返回 401 错误
        F-->>U: 显示错误信息
    end
```

#### 2.1.2 API 访问保护流程
```mermaid
sequenceDiagram
    participant F as 前端
    participant A as 后端 API
    participant M as 中间件

    F->>A: 携带 Bearer Token 请求 API
    A->>M: Token 验证中间件
    M->>M: 解析并验证 JWT Token
    
    alt Token 有效
        M->>M: 提取用户信息 (username)
        M->>A: 继续处理请求，注入用户上下文
        A-->>F: 返回数据
    else Token 无效/过期
        M-->>F: 返回 401 Unauthorized
    end
```

### 2.2 LDAP 集成设计

#### 2.2.1 LDAP 配置参数
```python
# LDAP 服务器配置
LDAP_URI = 'ldap://**************:389'
LDAP_BASE_DN_USERS = 'dc=users,dc=appdata,dc=erayt,dc=com'
LDAP_TIMEOUT = 3  # 连接超时时间（秒）
```

#### 2.2.2 LDAP 认证逻辑
- 使用 `cn={username},{basedn}` 格式构建用户 DN
- 采用 SIMPLE 认证方式进行绑定
- 处理连接超时和认证失败异常
- 记录详细的认证日志
- **认证成功后立即生成 JWT，不存储用户信息**

### 2.3 JWT 令牌设计

#### 2.3.1 JWT 配置
```python
# JWT 配置参数
SECRET_KEY = "your-secret-key-change-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60
```

#### 2.3.2 JWT Payload 结构
```json
{
  "sub": "username",           // 用户名 (LDAP 用户标识)
  "exp": 1703123456,          // 过期时间
  "iat": 1703120000,          // 签发时间
  "type": "access_token"      // 令牌类型
}
```

## 3. 数据库设计

### 3.1 数据模型

**注意**: 不再需要用户表，用户信息完全依赖 JWT Token 中的 username

#### 3.1.1 会话表 (Conversation)
```sql
CREATE TABLE conversation (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL,  -- 直接存储 LDAP 用户名
    title VARCHAR(255) DEFAULT '新的对话',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_created_at (created_at)
);
```

#### 3.1.2 消息表 (Message)
```sql
CREATE TABLE message (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    conversation_id INTEGER NOT NULL,
    role VARCHAR(20) NOT NULL,  -- 'user' 或 'assistant'
    content TEXT NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES conversation(id) ON DELETE CASCADE,
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_timestamp (timestamp)
);
```

### 3.2 数据关系图
```
Conversation (1) ────── (N) Message
      │                        │
      └─ username              └─ conversation_id
         title                    role
         created_at               content
         updated_at               timestamp
```

## 4. API 设计

### 4.1 认证相关 API

#### 4.1.1 用户登录
```http
POST /api/login
Content-Type: application/json

{
  "username": "zhangsan",
  "password": "password123"
}
```

**成功响应 (200)**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "user": {
    "username": "zhangsan"
  }
}
```

**失败响应 (401)**:
```json
{
  "detail": "用户名或密码错误，或LDAP服务不可用"
}
```

#### 4.1.2 用户信息获取
```http
GET /api/user/me
Authorization: Bearer {access_token}
```

**响应 (200)**:
```json
{
  "username": "zhangsan"
}
```

### 4.2 会话管理 API

#### 4.2.1 获取会话列表
```http
GET /api/conversations
Authorization: Bearer {access_token}
```

**响应 (200)**:
```json
[
  {
    "id": 1,
    "title": "新的对话",
    "created_at": "2024-01-15T10:30:00Z"
  }
]
```

#### 4.2.2 创建新会话
```http
POST /api/conversations/new
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "title": "关于 AI 的讨论"
}
```

#### 4.2.3 删除会话
```http
DELETE /api/conversations/{conversation_id}
Authorization: Bearer {access_token}
```

### 4.3 消息相关 API

#### 4.3.1 获取历史消息
```http
GET /api/messages?conversation_id=123
Authorization: Bearer {access_token}
```

#### 4.3.2 发送消息 (SSE 流式)
```http
POST /api/chat/stream
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "conversation_id": 123,
  "message": "你好，请介绍一下人工智能"
}
```

**SSE 响应**:
```
data: {"content": "人工智能"}
data: {"content": "（AI）是"}
data: {"content": "计算机科学"}
...
data: [DONE]
```

## 5. 系统架构实现

### 5.1 项目结构
```
backend/
├── api/                    # API 路由层
│   ├── __init__.py
│   ├── auth.py            # 认证相关路由
│   ├── chat.py            # 聊天相关路由
│   ├── conversations.py   # 会话管理路由
│   └── messages.py        # 消息管理路由
├── config/                # 配置管理
│   ├── __init__.py
│   └── settings.py        # 应用配置
├── db/                    # 数据库相关
│   ├── __init__.py
│   └── database.py        # 数据库连接和会话
├── models/                # 数据模型
│   ├── __init__.py
│   ├── conversation.py   # 会话模型
│   └── message.py        # 消息模型
├── schemas/               # Pydantic 模式
│   ├── __init__.py
│   ├── auth.py           # 认证相关模式
│   ├── conversation.py   # 会话相关模式
│   └── message.py        # 消息相关模式
├── services/              # 业务逻辑层
│   ├── __init__.py
│   ├── auth_service.py   # 认证服务
│   ├── ldap_service.py   # LDAP 集成服务
│   ├── conversation_service.py  # 会话服务
│   └── message_service.py       # 消息服务
├── utils/                 # 工具函数
│   ├── __init__.py
│   ├── dependencies.py   # FastAPI 依赖
│   └── security.py       # 安全相关工具
├── docs/                  # 文档目录
├── main.py               # 应用入口
├── pyproject.toml        # 项目配置和依赖
└── .gitignore           # Git 忽略文件
```

### 5.2 核心服务实现

#### 5.2.1 LDAP 认证服务
```python
# services/ldap_service.py
from ldap3 import Server, Connection, ALL, core
import logging

class LDAPService:
    def __init__(self, ldap_uri: str, base_dn: str):
        self.ldap_uri = ldap_uri
        self.base_dn = base_dn
        self.logger = logging.getLogger(__name__)
    
    def authenticate_user(self, username: str, password: str) -> bool:
        """LDAP 用户认证"""
        user_dn = f"cn={username},{self.base_dn}"
        server = Server(self.ldap_uri, get_info=ALL, connect_timeout=3)
        
        try:
            conn = Connection(
                server,
                user=user_dn,
                password=password,
                authentication='SIMPLE',
                auto_bind=True,
            )
            conn.unbind()
            self.logger.info(f"[LDAP认证成功]: {username}")
            return True
        except core.exceptions.LDAPBindError as e:
            self.logger.error(f"[LDAP认证失败]: {username}, {e}")
            return False
        except Exception as e:
            self.logger.error(f"[LDAP连接失败]: {username}, {e}")
            return False
```

#### 5.2.2 认证中间件
```python
# utils/dependencies.py
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from config.settings import settings

security = HTTPBearer()

async def get_current_username(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """获取当前认证用户名"""
    try:
        payload = jwt.decode(
            credentials.credentials,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据"
            )
        return username
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据"
        )
```

#### 5.2.3 简化的认证服务
```python
# services/auth_service.py
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from config.settings import settings
from services.ldap_service import LDAPService

class AuthService:
    """简化的认证服务，不涉及数据库操作"""

    def __init__(self):
        self.ldap_service = LDAPService(
            settings.LDAP_URI,
            settings.LDAP_BASE_DN,
            settings.LDAP_TIMEOUT
        )

    def authenticate_user(self, username: str, password: str) -> bool:
        """
        用户认证（仅 LDAP 验证）
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            bool: 认证成功返回 True，否则返回 False
        """
        return self.ldap_service.authenticate_user(username, password)

    def create_access_token(
        self, username: str, expires_delta: Optional[timedelta] = None
    ) -> str:
        """创建 JWT 访问令牌"""
        to_encode = {"sub": username}
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )

        to_encode.update({"exp": expire, "iat": datetime.utcnow()})
        encoded_jwt = jwt.encode(
            to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
        )
        return encoded_jwt

    def verify_token(self, token: str) -> Optional[str]:
        """验证 JWT 令牌并返回用户名"""
        try:
            payload = jwt.decode(
                token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
            )
            return payload.get("sub")
        except JWTError:
            return None
```

## 6. 安全考虑

### 6.1 认证安全
- **密码传输**: 生产环境必须使用 HTTPS
- **JWT 密钥**: 使用强随机密钥，定期轮换
- **Token 过期**: 设置合理的过期时间（60分钟）
- **LDAP 连接**: 设置连接超时，防止长时间阻塞

### 6.2 授权安全
- **用户隔离**: 每个用户只能访问自己的会话和消息（基于 JWT 中的 username）
- **API 保护**: 所有受保护的 API 都需要有效的 JWT Token
- **权限检查**: 在服务层基于 username 进行权限验证

### 6.3 数据安全
- **SQL 注入**: 使用 SQLAlchemy ORM 防止 SQL 注入
- **XSS 防护**: 前端输出时进行 HTML 转义
- **CORS 配置**: 限制允许的源域名

## 7. 性能优化

### 7.1 数据库优化
- **索引策略**: 在 username、conversation_id 等字段上建立索引
- **查询优化**: 使用分页查询，避免一次加载大量数据
- **连接池**: 配置合适的数据库连接池

### 7.2 缓存策略
- **会话缓存**: 缓存活跃会话的基本信息
- **LDAP 连接复用**: 优化 LDAP 连接管理

### 7.3 并发处理
- **异步 I/O**: 使用 FastAPI 的异步特性
- **流式响应**: 实现 SSE 流式消息推送
- **连接管理**: 合理管理 LDAP 和数据库连接

## 10. 监控和日志

### 10.1 日志记录
- **认证日志**: 记录登录成功/失败
- **LDAP 日志**: 记录 LDAP 连接状态
- **API 日志**: 记录 API 请求和响应
- **错误日志**: 记录系统异常和错误

### 10.2 监控指标
- **响应时间**: API 响应时间监控
- **错误率**: 认证失败率、API 错误率
- **并发数**: 同时在线用户数
- **资源使用**: CPU、内存、数据库连接数 
