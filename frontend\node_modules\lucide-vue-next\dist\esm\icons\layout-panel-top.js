/**
 * lucide-vue-next v0.292.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const LayoutPanelTop = createLucideIcon("LayoutPanelTopIcon", [
  [
    "rect",
    { width: "18", height: "7", x: "3", y: "3", rx: "1", key: "f1a2em" }
  ],
  [
    "rect",
    { width: "7", height: "7", x: "3", y: "14", rx: "1", key: "1bb6yr" }
  ],
  [
    "rect",
    { width: "7", height: "7", x: "14", y: "14", rx: "1", key: "nxv5o0" }
  ]
]);

export { LayoutPanelTop as default };
//# sourceMappingURL=layout-panel-top.js.map
