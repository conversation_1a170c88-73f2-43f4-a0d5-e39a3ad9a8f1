from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any
from milvus_search import VectorStore_Search
import uvicorn

# 配置参数
MODEL_EMBEDDING_DIM = 1024
MODEL_EMBEDDING = 'bge-m3'
MODEL_CHAT = 'qwen3-32b'
MODEL_RERANK = 'bge-reranker-v2-m3'
API_KEY = 'sk-LLFZSeNxq59tCMCoA2Fc909a5a5d4720B0188556F67a7553'
API_URL = 'http://**************:23000/v1'
API_RERANK_URL ='http://**************:9997/'
MILVUS_URL = 'http://**************:19530'
MILVUS_USER = 'dify'
MILVUS_PASSWORD = 'dify2025'

# 初始化FastAPI应用
app = FastAPI(
    title="向量搜索API",
    description="基于Milvus的向量搜索服务，支持语义搜索、关键字搜索、混合搜索等功能",
    version="1.0.0"
)

# 初始化向量搜索实例
milvus = VectorStore_Search(
    milvus_url=MILVUS_URL,
    milvus_user=MILVUS_USER,
    milvus_password=MILVUS_PASSWORD,
    db_name="erayt_wiki",
    collection_name="rcs",
    chunk_strategy="parent_child",
    embedding_model=MODEL_EMBEDDING,
    rerank_url=API_RERANK_URL,
    openai_url=API_URL,
    openai_api_key=API_KEY,
)

# Pydantic模型定义
class SearchRequest(BaseModel):
    query: str = Field(..., description="搜索查询文本")
    k: int = Field(default=5, ge=1, le=100, description="返回结果数量")
    filter_conditions: Optional[Dict[str, Any]] = Field(default=None, description="过滤条件")
    min_score: float = Field(default=0, ge=0, le=1, description="最小相似度分数")

class ParentChildSearchRequest(SearchRequest):
    sort_by: str = Field(default='total_score', description="排序方式")

class HybridSearchRequest(SearchRequest):
    semantic_weight: float = Field(default=0.7, ge=0, le=1, description="语义搜索权重")

class HybridParentChildSearchRequest(ParentChildSearchRequest):
    semantic_weight: float = Field(default=0.5, ge=0, le=1, description="语义搜索权重")

class NeighborsSearchRequest(SearchRequest):
    p: int = Field(default=2, ge=1, le=10, description="邻居扩展层级")
    sort_by: str = Field(default='total_score', description="排序方式")

class HybridNeighborsSearchRequest(NeighborsSearchRequest):
    semantic_weight: float = Field(default=0.5, ge=0, le=1, description="语义搜索权重")

class RerankRequest(BaseModel):
    query: str = Field(..., description="重排序查询文本")
    documents: List[Dict[str, Any]] = Field(..., description="待重排序的文档列表")
    top_k: Optional[int] = Field(default=None, description="重排序后返回的文档数量")
    rerank_threshold: float = Field(default=0.0, ge=0, le=1, description="重排序阈值")
    model: str = Field(default="bge-reranker-v2-m3", description="重排序模型")

class SearchResponse(BaseModel):
    success: bool = Field(default=True, description="请求是否成功")
    message: str = Field(default="搜索成功", description="响应消息")
    data: List[Dict[str, Any]] = Field(default=[], description="搜索结果")
    total: int = Field(default=0, description="结果总数")

# 辅助函数：构建动态参数字典
def build_search_params(request: BaseModel, exclude_fields: set = None) -> dict:
    """构建搜索参数字典，排除默认值和指定字段"""
    if exclude_fields is None:
        exclude_fields = set()
    
    params = {"query": request.query}
    
    # 只有当值不是默认值时才添加参数
    for field_name, field_info in request.__class__.__fields__.items():
        if field_name in exclude_fields or field_name == "query":
            continue
            
        value = getattr(request, field_name)
        default_value = field_info.default
        
        # 只添加非默认值的参数
        if value != default_value:
            params[field_name] = value
    
    return params

# API端点定义

@app.post("/search/semantic", response_model=SearchResponse, summary="语义搜索", description="基于语义相似度的向量搜索")
async def search_semantic(request: SearchRequest):
    """语义搜索API"""
    try:
        params = build_search_params(request)
        results = milvus.search_semantic(**params)
        return SearchResponse(
            success=True,
            message="语义搜索成功",
            data=results,
            total=len(results)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"语义搜索失败: {str(e)}")

@app.post("/search/keyword", response_model=SearchResponse, summary="关键字搜索", description="基于关键字匹配的稀疏向量搜索")
async def search_keyword(request: SearchRequest):
    """关键字搜索API"""
    try:
        params = build_search_params(request)
        results = milvus.search_keyword(**params)
        return SearchResponse(
            success=True,
            message="关键字搜索成功",
            data=results,
            total=len(results)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"关键字搜索失败: {str(e)}")

@app.post("/search/hybrid", response_model=SearchResponse, summary="混合搜索", description="语义搜索和关键字搜索的混合")
async def search_hybrid(request: HybridSearchRequest):
    """混合搜索API"""
    try:
        params = build_search_params(request)
        results = milvus.search_hybrid(**params)
        return SearchResponse(
            success=True,
            message="混合搜索成功",
            data=results,
            total=len(results)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"混合搜索失败: {str(e)}")

@app.post("/search/keyword-parent-child", response_model=SearchResponse, summary="关键字父子搜索", description="基于父子关系的关键字搜索")
async def search_keyword_parent_child(request: ParentChildSearchRequest):
    """关键字父子搜索API"""
    try:
        params = build_search_params(request)
        results = milvus.search_keyword_parent_child(**params)
        return SearchResponse(
            success=True,
            message="关键字父子搜索成功",
            data=results,
            total=len(results)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"关键字父子搜索失败: {str(e)}")

@app.post("/search/semantic-parent-child", response_model=SearchResponse, summary="语义父子搜索", description="基于父子关系的语义搜索")
async def search_semantic_parent_child(request: ParentChildSearchRequest):
    """语义父子搜索API"""
    try:
        params = build_search_params(request)
        results = milvus.search_semantic_parent_child(**params)
        return SearchResponse(
            success=True,
            message="语义父子搜索成功",
            data=results,
            total=len(results)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"语义父子搜索失败: {str(e)}")

@app.post("/search/hybrid-parent-child", response_model=SearchResponse, summary="混合父子搜索", description="基于父子关系的混合搜索")
async def search_hybrid_parent_child(request: HybridParentChildSearchRequest):
    """混合父子搜索API"""
    try:
        params = build_search_params(request)
        print(params)
        results = milvus.search_hybrid_parent_child(**params)
        return SearchResponse(
            success=True,
            message="混合父子搜索成功",
            data=results,
            total=len(results)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"混合父子搜索失败: {str(e)}")

@app.post("/rerank", response_model=SearchResponse, summary="重排序", description="对搜索结果进行重排序")
async def rerank(request: RerankRequest):
    """重排序API"""
    try:
        params = build_search_params(request)
        results = milvus.rerank(**params)
        return SearchResponse(
            success=True,
            message="重排序成功",
            data=results,
            total=len(results)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重排序失败: {str(e)}")

@app.post("/search/keyword-neighbors", response_model=SearchResponse, summary="关键字邻居搜索", description="基于邻居扩展的关键字搜索")
async def search_keyword_neighbors(request: NeighborsSearchRequest):
    """关键字邻居搜索API"""
    try:
        params = build_search_params(request)
        results = milvus.search_keyword_neighbors(**params)
        return SearchResponse(
            success=True,
            message="关键字邻居搜索成功",
            data=results,
            total=len(results)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"关键字邻居搜索失败: {str(e)}")

@app.post("/search/semantic-neighbors", response_model=SearchResponse, summary="语义邻居搜索", description="基于邻居扩展的语义搜索")
async def search_semantic_neighbors(request: NeighborsSearchRequest):
    """语义邻居搜索API"""
    try:
        params = build_search_params(request)
        results = milvus.search_semantic_neighbors(**params)
        return SearchResponse(
            success=True,
            message="语义邻居搜索成功",
            data=results,
            total=len(results)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"语义邻居搜索失败: {str(e)}")

@app.post("/search/hybrid-neighbors", response_model=SearchResponse, summary="混合邻居搜索", description="基于邻居扩展的混合搜索")
async def search_hybrid_neighbors(request: HybridNeighborsSearchRequest):
    """混合邻居搜索API"""
    try:
        params = build_search_params(request)
        results = milvus.search_hybrid_neighbors(**params)
        return SearchResponse(
            success=True,
            message="混合邻居搜索成功",
            data=results,
            total=len(results)
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"混合邻居搜索失败: {str(e)}")

@app.get("/health", summary="健康检查", description="检查服务状态")
async def health_check():
    """健康检查API"""
    return {"status": "healthy", "message": "向量搜索服务运行正常"}

@app.get("/", summary="根路径", description="API根路径信息")
async def root():
    """根路径信息"""
    return {
        "name": "向量搜索API",
        "version": "1.0.0",
        "description": "基于Milvus的向量搜索服务",
        "docs_url": "/docs",
        "endpoints": [
            "/search/semantic",
            "/search/keyword", 
            "/search/hybrid",
            "/search/keyword-parent-child",
            "/search/semantic-parent-child",
            "/search/hybrid-parent-child",
            "/rerank",
            "/search/keyword-neighbors",
            "/search/semantic-neighbors",
            "/search/hybrid-neighbors"
        ]
    }

if __name__ == "__main__":
    uvicorn.run(
        "api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
