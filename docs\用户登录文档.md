```python
from flask import Flask, render_template, request
from ldap3 import Server, Connection, ALL, core
import logging

app = Flask(__name__)

LDAP_URI = 'ldap://192.168.10.113:389'
LDAP_BASE_DN_USERS = 'dc=users,dc=appdata,dc=erayt,dc=com'

logger = logging.getLogger(__name__)

def get_user_dn(username: str, basedn: str) -> str:
    # 按你的 ldapsearch 方式拼接
    return f"cn={username},{basedn}"

def valid_user(ldap_uri: str, basedn: str, username: str, password: str) -> bool:
    user_dn = get_user_dn(username, basedn)
    print(f"尝试绑定的DN: {user_dn}")
    server = Server(ldap_uri, get_info=ALL, connect_timeout=3)
    try:
        conn = Connection(
            server,
            user=user_dn,
            password=password,
            authentication='SIMPLE',
            auto_bind=True,
        )
        conn.unbind()
        logger.info(f"[LDAP用户认证成功]：{username}")
        return True
    except core.exceptions.LDAPBindError as e:
        logger.error(f"[LDAP用户认证失败]：{username}, {e}")
        return False
    except Exception as e:
        logger.error(f"[LDAP服务连接失败]：{username}, {e}")
        return False

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        success = valid_user(LDAP_URI, LDAP_BASE_DN_USERS, username, password)
        if success:
            message = f"用户 {username} 登录成功"
        else:
            message = "用户名或密码错误，或LDAP服务不可用"
        return render_template('result.html', success=success, message=message)
    return render_template('login.html')

if __name__ == '__main__':
    app.run(debug=True)
```
## 登录接口需求如下：
1. 用户在页面输入username和password之后，调用后端登录接口，后端调用外部的LDAP服务验证账号密码
2. 如果LADP返回失败，前端则显示用户名或密码错误，或LDAP服务不可用


## 鉴权主要流程：
1. 用户登录 → 使用username获取 token（如 JWT）
2. 每个受保护的接口在访问时校验 token（身份验证）
3. 根据用户身份/角色/资源，判断是否有权限（权限验证）



```mermaid
sequenceDiagram
    participant U as 用户
    participant API as FastAPI服务
    participant DB as LDAP服务
    
    U->>API: POST /login（用户名+密码）
    API->>DB: 查询用户密码
    DB-->>API: 返回是否登陆成功
    API-->>U: 使用username生成JWT,返回 access_token（JWT）

    U->>API: 携带 Authorization: Bearer <token> 访问受保护接口
    API->>API: 验证 token 是否合法
    API-->>U: 返回受保护数据（已登录）
```



### ✅ 技术选型

| 项目       | 技术                                         |
| ---------- | -------------------------------------------- |
| 鉴权协议   | OAuth2 Password Flow                         |
| Token 格式 | JWT（JSON Web Token）                        |
| 加密算法   | HS256（对称密钥）                            |
| 工具库     | `fastapi.security`, `python-jose`, `passlib` |
