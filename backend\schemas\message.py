"""Message schemas for request/response validation."""

from datetime import datetime
from pydantic import BaseModel
from typing import Dict, List


class MessageResponse(BaseModel):
    """Message response schema."""

    role: str
    content: str
    timestamp: datetime

    model_config = {"from_attributes": True}


class ChatRequest(BaseModel):
    """Chat request schema for sending messages."""

    conversation_id: int
    message: str


class MessagesResponse(BaseModel):
    """Messages response schema with conversation ID as key."""

    messages: Dict[int, List[MessageResponse]]
