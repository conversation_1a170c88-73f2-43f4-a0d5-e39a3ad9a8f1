好的，这里为你提供一份**FastAPI 用户登录验证的设计文档（基础权限：只校验是否登录）**，用于确保用户访问受保护接口时必须先登录，具备完整性和可落地性。

------

# 🛡️ FastAPI 用户登录验证设计文档（基础权限控制）

------

## 一、目标说明

- 系统中部分接口需要用户先登录后才能访问。
- 登录后，用户通过 JWT Token 来访问受保护资源。
- 只需判断用户是否登录，无需复杂角色控制。

------

## 二、技术方案

### ✅ 技术选型

| 项目       | 技术                                         |
| ---------- | -------------------------------------------- |
| 鉴权协议   | OAuth2 Password Flow                         |
| Token 格式 | JWT（JSON Web Token）                        |
| 加密算法   | HS256（对称密钥）                            |
| 工具库     | `fastapi.security`, `python-jose`, `passlib` |

------

## 三、流程说明

```mermaid
sequenceDiagram
    participant U as 用户
    participant API as FastAPI服务
    participant DB as 数据库
    
    U->>API: POST /login（用户名+密码）
    API->>DB: 查询用户密码哈希
    DB-->>API: 返回用户数据
    API->>API: 验证密码
    API-->>U: 返回 access_token（JWT）

    U->>API: 携带 Authorization: Bearer <token> 访问受保护接口
    API->>API: 验证 token 是否合法
    API-->>U: 返回受保护数据（已登录）
```

------

## 四、模块设计

### 4.1 登录模块

- 路由：`POST /login`
- 验证用户密码是否正确
- 生成并返回 JWT Token

### 4.2 依赖项模块：验证是否已登录

- 路由中通过 `Depends(get_current_user)` 来校验
- 校验 token 的合法性和是否过期
- 验证通过后返回用户信息对象

------

## 五、接口设计

### 5.1 登录接口

- 路由：`POST /login`
- 类型：公开
- 说明：提交用户名和密码，返回 JWT token

#### 请求示例：

```json
{
  "username": "test",
  "password": "123456"
}
```

#### 响应示例：

```json
{
  "access_token": "xxxxx.yyyyy.zzzzz",
  "token_type": "bearer"
}
```

------

### 5.2 获取当前用户信息（受保护）

- 路由：`GET /me`
- 类型：受保护
- 说明：验证 token 是否有效，返回当前用户信息

#### 请求头：

```
Authorization: Bearer <access_token>
```

#### 响应示例：

```json
{
  "id": 1,
  "username": "test",
  "created_at": "2025-07-24T12:00:00"
}
```

------

## 六、代码结构设计（简要）

```
backend/
├── main.py
├── api/
│   ├── auth.py           # 登录接口
│   └── protected.py      # 受保护接口示例
├── core/
│   ├── security.py       # token生成、验证逻辑
│   └── dependencies.py   # get_current_user等依赖
├── models/
│   └── user.py
├── db/
│   └── session.py        # DB连接
└── schemas/
    └── user.py
```

------

## 七、安全说明

- 密码使用 `bcrypt` 加密存储
- JWT 中包含 `sub` 字段作为 username、`exp` 字段控制过期时间
- Token 通过 `Authorization: Bearer` 方式传递
- 后端接口使用 `Depends(get_current_user)` 验证是否登录

------

## 八、扩展建议（非必需）

| 功能         | 建议               |
| ------------ | ------------------ |
| 自动续签     | 增加 refresh token |
| 用户登出     | JWT 黑名单机制     |
| 多设备登录   | token 与设备绑定   |
| 缓存用户信息 | Redis等            |

------

## 九、参考接口依赖示意（FastAPI代码层）

```python
# 依赖函数
def get_current_user(token: str = Depends(oauth2_scheme)) -> User:
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username = payload.get("sub")
        if username is None:
            raise credentials_exception
        return get_user_by_username(username)
    except JWTError:
        raise credentials_exception

# 使用依赖
@app.get("/me")
def read_current_user(current_user: User = Depends(get_current_user)):
    return current_user
```

------

## ✅ 结论

本设计提供了一个轻量但完整的“登录校验”机制，适用于：

- 用户必须登录后访问的聊天系统、后台管理系统等
- 无需复杂权限判断，仅判断“是否登录”

如你需要，我可以继续为你生成：

- 登录和认证的完整代码模板
- Vue3 端的登录处理及 token 存储逻辑

是否继续？