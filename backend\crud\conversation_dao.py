from sqlalchemy.orm import Session
from typing import List, Optional
from models.conversation import Conversation


class ConversationDao:
    @staticmethod
    def get_by_id(
        db: Session, conversation_id: int, username: Optional[str] = None
    ) -> Optional[Conversation]:
        query = db.query(Conversation).filter(Conversation.id == conversation_id)
        if username:
            query = query.filter(Conversation.username == username)
        return query.first()

    @staticmethod
    def get_by_username(db: Session, username: str) -> List[Conversation]:
        return (
            db.query(Conversation)
            .filter(Conversation.username == username)
            .order_by(Conversation.created_at.desc())
            .all()
        )

    @staticmethod
    def create(db: Session, username: str, title: str = "新的对话") -> Conversation:
        conversation = Conversation(username=username, title=title)
        db.add(conversation)
        db.commit()
        db.refresh(conversation)
        return conversation

    @staticmethod
    def delete(db: Session, conversation: Conversation) -> None:
        db.delete(conversation)
        db.commit()
