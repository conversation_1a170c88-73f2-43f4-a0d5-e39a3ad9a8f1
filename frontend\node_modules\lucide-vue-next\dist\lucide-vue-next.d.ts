import { SVGAttributes, DefineComponent } from 'vue';
declare module 'lucide-vue-next'

// Create interface extending SVGAttributes
export interface SVGProps extends Partial<SVGAttributes> {
  size?: 24 | number
  strokeWidth?: number | string
  absoluteStrokeWidth?: boolean
}

export type Icon = DefineComponent<SVGProps>

// Generated icons
export declare const Accessibility: Icon;
export declare const ActivitySquare: Icon;
export declare const Activity: Icon;
export declare const AirVent: Icon;
export declare const Airplay: Icon;
export declare const AlarmCheck: Icon;
export declare const AlarmClockOff: Icon;
export declare const AlarmClock: Icon;
export declare const AlarmMinus: Icon;
export declare const AlarmPlus: Icon;
export declare const Album: Icon;
export declare const AlertCircle: Icon;
export declare const AlertOctagon: Icon;
export declare const AlertTriangle: Icon;
export declare const AlignCenterHorizontal: Icon;
export declare const AlignCenterVertical: Icon;
export declare const AlignCenter: Icon;
export declare const AlignEndHorizontal: Icon;
export declare const AlignEndVertical: Icon;
export declare const AlignHorizontalDistributeCenter: Icon;
export declare const AlignHorizontalDistributeEnd: Icon;
export declare const AlignHorizontalDistributeStart: Icon;
export declare const AlignHorizontalJustifyCenter: Icon;
export declare const AlignHorizontalJustifyEnd: Icon;
export declare const AlignHorizontalJustifyStart: Icon;
export declare const AlignHorizontalSpaceAround: Icon;
export declare const AlignHorizontalSpaceBetween: Icon;
export declare const AlignJustify: Icon;
export declare const AlignLeft: Icon;
export declare const AlignRight: Icon;
export declare const AlignStartHorizontal: Icon;
export declare const AlignStartVertical: Icon;
export declare const AlignVerticalDistributeCenter: Icon;
export declare const AlignVerticalDistributeEnd: Icon;
export declare const AlignVerticalDistributeStart: Icon;
export declare const AlignVerticalJustifyCenter: Icon;
export declare const AlignVerticalJustifyEnd: Icon;
export declare const AlignVerticalJustifyStart: Icon;
export declare const AlignVerticalSpaceAround: Icon;
export declare const AlignVerticalSpaceBetween: Icon;
export declare const Ampersand: Icon;
export declare const Ampersands: Icon;
export declare const Anchor: Icon;
export declare const Angry: Icon;
export declare const Annoyed: Icon;
export declare const Antenna: Icon;
export declare const Aperture: Icon;
export declare const AppWindow: Icon;
export declare const Apple: Icon;
export declare const ArchiveRestore: Icon;
export declare const ArchiveX: Icon;
export declare const Archive: Icon;
export declare const AreaChart: Icon;
export declare const Armchair: Icon;
export declare const ArrowBigDownDash: Icon;
export declare const ArrowBigDown: Icon;
export declare const ArrowBigLeftDash: Icon;
export declare const ArrowBigLeft: Icon;
export declare const ArrowBigRightDash: Icon;
export declare const ArrowBigRight: Icon;
export declare const ArrowBigUpDash: Icon;
export declare const ArrowBigUp: Icon;
export declare const ArrowDown01: Icon;
export declare const ArrowDown10: Icon;
export declare const ArrowDownAZ: Icon;
export declare const ArrowDownCircle: Icon;
export declare const ArrowDownFromLine: Icon;
export declare const ArrowDownLeftFromCircle: Icon;
export declare const ArrowDownLeftSquare: Icon;
export declare const ArrowDownLeft: Icon;
export declare const ArrowDownNarrowWide: Icon;
export declare const ArrowDownRightFromCircle: Icon;
export declare const ArrowDownRightSquare: Icon;
export declare const ArrowDownRight: Icon;
export declare const ArrowDownSquare: Icon;
export declare const ArrowDownToDot: Icon;
export declare const ArrowDownToLine: Icon;
export declare const ArrowDownUp: Icon;
export declare const ArrowDownWideNarrow: Icon;
export declare const ArrowDownZA: Icon;
export declare const ArrowDown: Icon;
export declare const ArrowLeftCircle: Icon;
export declare const ArrowLeftFromLine: Icon;
export declare const ArrowLeftRight: Icon;
export declare const ArrowLeftSquare: Icon;
export declare const ArrowLeftToLine: Icon;
export declare const ArrowLeft: Icon;
export declare const ArrowRightCircle: Icon;
export declare const ArrowRightFromLine: Icon;
export declare const ArrowRightLeft: Icon;
export declare const ArrowRightSquare: Icon;
export declare const ArrowRightToLine: Icon;
export declare const ArrowRight: Icon;
export declare const ArrowUp01: Icon;
export declare const ArrowUp10: Icon;
export declare const ArrowUpAZ: Icon;
export declare const ArrowUpCircle: Icon;
export declare const ArrowUpDown: Icon;
export declare const ArrowUpFromDot: Icon;
export declare const ArrowUpFromLine: Icon;
export declare const ArrowUpLeftFromCircle: Icon;
export declare const ArrowUpLeftSquare: Icon;
export declare const ArrowUpLeft: Icon;
export declare const ArrowUpNarrowWide: Icon;
export declare const ArrowUpRightFromCircle: Icon;
export declare const ArrowUpRightSquare: Icon;
export declare const ArrowUpRight: Icon;
export declare const ArrowUpSquare: Icon;
export declare const ArrowUpToLine: Icon;
export declare const ArrowUpWideNarrow: Icon;
export declare const ArrowUpZA: Icon;
export declare const ArrowUp: Icon;
export declare const ArrowsUpFromLine: Icon;
export declare const Asterisk: Icon;
export declare const AtSign: Icon;
export declare const Atom: Icon;
export declare const Award: Icon;
export declare const Axe: Icon;
export declare const Axis3d: Icon;
export declare const Baby: Icon;
export declare const Backpack: Icon;
export declare const BadgeAlert: Icon;
export declare const BadgeCent: Icon;
export declare const BadgeCheck: Icon;
export declare const BadgeDollarSign: Icon;
export declare const BadgeEuro: Icon;
export declare const BadgeHelp: Icon;
export declare const BadgeIndianRupee: Icon;
export declare const BadgeInfo: Icon;
export declare const BadgeJapaneseYen: Icon;
export declare const BadgeMinus: Icon;
export declare const BadgePercent: Icon;
export declare const BadgePlus: Icon;
export declare const BadgePoundSterling: Icon;
export declare const BadgeRussianRuble: Icon;
export declare const BadgeSwissFranc: Icon;
export declare const BadgeX: Icon;
export declare const Badge: Icon;
export declare const BaggageClaim: Icon;
export declare const Ban: Icon;
export declare const Banana: Icon;
export declare const Banknote: Icon;
export declare const BarChart2: Icon;
export declare const BarChart3: Icon;
export declare const BarChart4: Icon;
export declare const BarChartBig: Icon;
export declare const BarChartHorizontalBig: Icon;
export declare const BarChartHorizontal: Icon;
export declare const BarChart: Icon;
export declare const Barcode: Icon;
export declare const Baseline: Icon;
export declare const Bath: Icon;
export declare const BatteryCharging: Icon;
export declare const BatteryFull: Icon;
export declare const BatteryLow: Icon;
export declare const BatteryMedium: Icon;
export declare const BatteryWarning: Icon;
export declare const Battery: Icon;
export declare const Beaker: Icon;
export declare const BeanOff: Icon;
export declare const Bean: Icon;
export declare const BedDouble: Icon;
export declare const BedSingle: Icon;
export declare const Bed: Icon;
export declare const Beef: Icon;
export declare const Beer: Icon;
export declare const BellDot: Icon;
export declare const BellMinus: Icon;
export declare const BellOff: Icon;
export declare const BellPlus: Icon;
export declare const BellRing: Icon;
export declare const Bell: Icon;
export declare const Bike: Icon;
export declare const Binary: Icon;
export declare const Biohazard: Icon;
export declare const Bird: Icon;
export declare const Bitcoin: Icon;
export declare const Blinds: Icon;
export declare const Blocks: Icon;
export declare const BluetoothConnected: Icon;
export declare const BluetoothOff: Icon;
export declare const BluetoothSearching: Icon;
export declare const Bluetooth: Icon;
export declare const Bold: Icon;
export declare const Bomb: Icon;
export declare const Bone: Icon;
export declare const BookA: Icon;
export declare const BookAudio: Icon;
export declare const BookCheck: Icon;
export declare const BookCopy: Icon;
export declare const BookDashed: Icon;
export declare const BookDown: Icon;
export declare const BookHeadphones: Icon;
export declare const BookHeart: Icon;
export declare const BookImage: Icon;
export declare const BookKey: Icon;
export declare const BookLock: Icon;
export declare const BookMarked: Icon;
export declare const BookMinus: Icon;
export declare const BookOpenCheck: Icon;
export declare const BookOpenText: Icon;
export declare const BookOpen: Icon;
export declare const BookPlus: Icon;
export declare const BookText: Icon;
export declare const BookType: Icon;
export declare const BookUp2: Icon;
export declare const BookUp: Icon;
export declare const BookUser: Icon;
export declare const BookX: Icon;
export declare const Book: Icon;
export declare const BookmarkCheck: Icon;
export declare const BookmarkMinus: Icon;
export declare const BookmarkPlus: Icon;
export declare const BookmarkX: Icon;
export declare const Bookmark: Icon;
export declare const BoomBox: Icon;
export declare const Bot: Icon;
export declare const BoxSelect: Icon;
export declare const Box: Icon;
export declare const Boxes: Icon;
export declare const Braces: Icon;
export declare const Brackets: Icon;
export declare const BrainCircuit: Icon;
export declare const BrainCog: Icon;
export declare const Brain: Icon;
export declare const Briefcase: Icon;
export declare const BringToFront: Icon;
export declare const Brush: Icon;
export declare const BugOff: Icon;
export declare const BugPlay: Icon;
export declare const Bug: Icon;
export declare const Building2: Icon;
export declare const Building: Icon;
export declare const BusFront: Icon;
export declare const Bus: Icon;
export declare const CableCar: Icon;
export declare const Cable: Icon;
export declare const CakeSlice: Icon;
export declare const Cake: Icon;
export declare const Calculator: Icon;
export declare const CalendarCheck2: Icon;
export declare const CalendarCheck: Icon;
export declare const CalendarClock: Icon;
export declare const CalendarDays: Icon;
export declare const CalendarHeart: Icon;
export declare const CalendarMinus: Icon;
export declare const CalendarOff: Icon;
export declare const CalendarPlus: Icon;
export declare const CalendarRange: Icon;
export declare const CalendarSearch: Icon;
export declare const CalendarX2: Icon;
export declare const CalendarX: Icon;
export declare const Calendar: Icon;
export declare const CameraOff: Icon;
export declare const Camera: Icon;
export declare const CandlestickChart: Icon;
export declare const CandyCane: Icon;
export declare const CandyOff: Icon;
export declare const Candy: Icon;
export declare const CarFront: Icon;
export declare const CarTaxiFront: Icon;
export declare const Car: Icon;
export declare const Caravan: Icon;
export declare const Carrot: Icon;
export declare const CaseLower: Icon;
export declare const CaseSensitive: Icon;
export declare const CaseUpper: Icon;
export declare const CassetteTape: Icon;
export declare const Cast: Icon;
export declare const Castle: Icon;
export declare const Cat: Icon;
export declare const CheckCheck: Icon;
export declare const CheckCircle2: Icon;
export declare const CheckCircle: Icon;
export declare const CheckSquare2: Icon;
export declare const CheckSquare: Icon;
export declare const Check: Icon;
export declare const ChefHat: Icon;
export declare const Cherry: Icon;
export declare const ChevronDownCircle: Icon;
export declare const ChevronDownSquare: Icon;
export declare const ChevronDown: Icon;
export declare const ChevronFirst: Icon;
export declare const ChevronLast: Icon;
export declare const ChevronLeftCircle: Icon;
export declare const ChevronLeftSquare: Icon;
export declare const ChevronLeft: Icon;
export declare const ChevronRightCircle: Icon;
export declare const ChevronRightSquare: Icon;
export declare const ChevronRight: Icon;
export declare const ChevronUpCircle: Icon;
export declare const ChevronUpSquare: Icon;
export declare const ChevronUp: Icon;
export declare const ChevronsDownUp: Icon;
export declare const ChevronsDown: Icon;
export declare const ChevronsLeftRight: Icon;
export declare const ChevronsLeft: Icon;
export declare const ChevronsRightLeft: Icon;
export declare const ChevronsRight: Icon;
export declare const ChevronsUpDown: Icon;
export declare const ChevronsUp: Icon;
export declare const Chrome: Icon;
export declare const Church: Icon;
export declare const CigaretteOff: Icon;
export declare const Cigarette: Icon;
export declare const CircleDashed: Icon;
export declare const CircleDollarSign: Icon;
export declare const CircleDotDashed: Icon;
export declare const CircleDot: Icon;
export declare const CircleEllipsis: Icon;
export declare const CircleEqual: Icon;
export declare const CircleOff: Icon;
export declare const CircleSlash2: Icon;
export declare const CircleSlash: Icon;
export declare const Circle: Icon;
export declare const CircuitBoard: Icon;
export declare const Citrus: Icon;
export declare const Clapperboard: Icon;
export declare const ClipboardCheck: Icon;
export declare const ClipboardCopy: Icon;
export declare const ClipboardEdit: Icon;
export declare const ClipboardList: Icon;
export declare const ClipboardPaste: Icon;
export declare const ClipboardSignature: Icon;
export declare const ClipboardType: Icon;
export declare const ClipboardX: Icon;
export declare const Clipboard: Icon;
export declare const Clock1: Icon;
export declare const Clock10: Icon;
export declare const Clock11: Icon;
export declare const Clock12: Icon;
export declare const Clock2: Icon;
export declare const Clock3: Icon;
export declare const Clock4: Icon;
export declare const Clock5: Icon;
export declare const Clock6: Icon;
export declare const Clock7: Icon;
export declare const Clock8: Icon;
export declare const Clock9: Icon;
export declare const Clock: Icon;
export declare const CloudCog: Icon;
export declare const CloudDrizzle: Icon;
export declare const CloudFog: Icon;
export declare const CloudHail: Icon;
export declare const CloudLightning: Icon;
export declare const CloudMoonRain: Icon;
export declare const CloudMoon: Icon;
export declare const CloudOff: Icon;
export declare const CloudRainWind: Icon;
export declare const CloudRain: Icon;
export declare const CloudSnow: Icon;
export declare const CloudSunRain: Icon;
export declare const CloudSun: Icon;
export declare const Cloud: Icon;
export declare const Cloudy: Icon;
export declare const Clover: Icon;
export declare const Club: Icon;
export declare const Code2: Icon;
export declare const Code: Icon;
export declare const Codepen: Icon;
export declare const Codesandbox: Icon;
export declare const Coffee: Icon;
export declare const Cog: Icon;
export declare const Coins: Icon;
export declare const Columns: Icon;
export declare const Combine: Icon;
export declare const Command: Icon;
export declare const Compass: Icon;
export declare const Component: Icon;
export declare const Computer: Icon;
export declare const ConciergeBell: Icon;
export declare const Cone: Icon;
export declare const Construction: Icon;
export declare const Contact2: Icon;
export declare const Contact: Icon;
export declare const Container: Icon;
export declare const Contrast: Icon;
export declare const Cookie: Icon;
export declare const CopyCheck: Icon;
export declare const CopyMinus: Icon;
export declare const CopyPlus: Icon;
export declare const CopySlash: Icon;
export declare const CopyX: Icon;
export declare const Copy: Icon;
export declare const Copyleft: Icon;
export declare const Copyright: Icon;
export declare const CornerDownLeft: Icon;
export declare const CornerDownRight: Icon;
export declare const CornerLeftDown: Icon;
export declare const CornerLeftUp: Icon;
export declare const CornerRightDown: Icon;
export declare const CornerRightUp: Icon;
export declare const CornerUpLeft: Icon;
export declare const CornerUpRight: Icon;
export declare const Cpu: Icon;
export declare const CreativeCommons: Icon;
export declare const CreditCard: Icon;
export declare const Croissant: Icon;
export declare const Crop: Icon;
export declare const Cross: Icon;
export declare const Crosshair: Icon;
export declare const Crown: Icon;
export declare const Cuboid: Icon;
export declare const CupSoda: Icon;
export declare const Currency: Icon;
export declare const Cylinder: Icon;
export declare const DatabaseBackup: Icon;
export declare const DatabaseZap: Icon;
export declare const Database: Icon;
export declare const Delete: Icon;
export declare const Dessert: Icon;
export declare const Diameter: Icon;
export declare const Diamond: Icon;
export declare const Dice1: Icon;
export declare const Dice2: Icon;
export declare const Dice3: Icon;
export declare const Dice4: Icon;
export declare const Dice5: Icon;
export declare const Dice6: Icon;
export declare const Dices: Icon;
export declare const Diff: Icon;
export declare const Disc2: Icon;
export declare const Disc3: Icon;
export declare const Disc: Icon;
export declare const DivideCircle: Icon;
export declare const DivideSquare: Icon;
export declare const Divide: Icon;
export declare const DnaOff: Icon;
export declare const Dna: Icon;
export declare const Dog: Icon;
export declare const DollarSign: Icon;
export declare const Donut: Icon;
export declare const DoorClosed: Icon;
export declare const DoorOpen: Icon;
export declare const Dot: Icon;
export declare const DownloadCloud: Icon;
export declare const Download: Icon;
export declare const DraftingCompass: Icon;
export declare const Drama: Icon;
export declare const Dribbble: Icon;
export declare const Droplet: Icon;
export declare const Droplets: Icon;
export declare const Drumstick: Icon;
export declare const Dumbbell: Icon;
export declare const EarOff: Icon;
export declare const Ear: Icon;
export declare const EggFried: Icon;
export declare const EggOff: Icon;
export declare const Egg: Icon;
export declare const EqualNot: Icon;
export declare const Equal: Icon;
export declare const Eraser: Icon;
export declare const Euro: Icon;
export declare const Expand: Icon;
export declare const ExternalLink: Icon;
export declare const EyeOff: Icon;
export declare const Eye: Icon;
export declare const Facebook: Icon;
export declare const Factory: Icon;
export declare const Fan: Icon;
export declare const FastForward: Icon;
export declare const Feather: Icon;
export declare const FerrisWheel: Icon;
export declare const Figma: Icon;
export declare const FileArchive: Icon;
export declare const FileAudio2: Icon;
export declare const FileAudio: Icon;
export declare const FileAxis3d: Icon;
export declare const FileBadge2: Icon;
export declare const FileBadge: Icon;
export declare const FileBarChart2: Icon;
export declare const FileBarChart: Icon;
export declare const FileBox: Icon;
export declare const FileCheck2: Icon;
export declare const FileCheck: Icon;
export declare const FileClock: Icon;
export declare const FileCode2: Icon;
export declare const FileCode: Icon;
export declare const FileCog: Icon;
export declare const FileDiff: Icon;
export declare const FileDigit: Icon;
export declare const FileDown: Icon;
export declare const FileEdit: Icon;
export declare const FileHeart: Icon;
export declare const FileImage: Icon;
export declare const FileInput: Icon;
export declare const FileJson2: Icon;
export declare const FileJson: Icon;
export declare const FileKey2: Icon;
export declare const FileKey: Icon;
export declare const FileLineChart: Icon;
export declare const FileLock2: Icon;
export declare const FileLock: Icon;
export declare const FileMinus2: Icon;
export declare const FileMinus: Icon;
export declare const FileOutput: Icon;
export declare const FilePieChart: Icon;
export declare const FilePlus2: Icon;
export declare const FilePlus: Icon;
export declare const FileQuestion: Icon;
export declare const FileScan: Icon;
export declare const FileSearch2: Icon;
export declare const FileSearch: Icon;
export declare const FileSignature: Icon;
export declare const FileSpreadsheet: Icon;
export declare const FileStack: Icon;
export declare const FileSymlink: Icon;
export declare const FileTerminal: Icon;
export declare const FileText: Icon;
export declare const FileType2: Icon;
export declare const FileType: Icon;
export declare const FileUp: Icon;
export declare const FileVideo2: Icon;
export declare const FileVideo: Icon;
export declare const FileVolume2: Icon;
export declare const FileVolume: Icon;
export declare const FileWarning: Icon;
export declare const FileX2: Icon;
export declare const FileX: Icon;
export declare const File: Icon;
export declare const Files: Icon;
export declare const Film: Icon;
export declare const FilterX: Icon;
export declare const Filter: Icon;
export declare const Fingerprint: Icon;
export declare const FishOff: Icon;
export declare const FishSymbol: Icon;
export declare const Fish: Icon;
export declare const FlagOff: Icon;
export declare const FlagTriangleLeft: Icon;
export declare const FlagTriangleRight: Icon;
export declare const Flag: Icon;
export declare const FlameKindling: Icon;
export declare const Flame: Icon;
export declare const FlashlightOff: Icon;
export declare const Flashlight: Icon;
export declare const FlaskConicalOff: Icon;
export declare const FlaskConical: Icon;
export declare const FlaskRound: Icon;
export declare const FlipHorizontal2: Icon;
export declare const FlipHorizontal: Icon;
export declare const FlipVertical2: Icon;
export declare const FlipVertical: Icon;
export declare const Flower2: Icon;
export declare const Flower: Icon;
export declare const Focus: Icon;
export declare const FoldHorizontal: Icon;
export declare const FoldVertical: Icon;
export declare const FolderArchive: Icon;
export declare const FolderCheck: Icon;
export declare const FolderClock: Icon;
export declare const FolderClosed: Icon;
export declare const FolderCog: Icon;
export declare const FolderDot: Icon;
export declare const FolderDown: Icon;
export declare const FolderEdit: Icon;
export declare const FolderGit2: Icon;
export declare const FolderGit: Icon;
export declare const FolderHeart: Icon;
export declare const FolderInput: Icon;
export declare const FolderKanban: Icon;
export declare const FolderKey: Icon;
export declare const FolderLock: Icon;
export declare const FolderMinus: Icon;
export declare const FolderOpenDot: Icon;
export declare const FolderOpen: Icon;
export declare const FolderOutput: Icon;
export declare const FolderPlus: Icon;
export declare const FolderRoot: Icon;
export declare const FolderSearch2: Icon;
export declare const FolderSearch: Icon;
export declare const FolderSymlink: Icon;
export declare const FolderSync: Icon;
export declare const FolderTree: Icon;
export declare const FolderUp: Icon;
export declare const FolderX: Icon;
export declare const Folder: Icon;
export declare const Folders: Icon;
export declare const Footprints: Icon;
export declare const Forklift: Icon;
export declare const FormInput: Icon;
export declare const Forward: Icon;
export declare const Frame: Icon;
export declare const Framer: Icon;
export declare const Frown: Icon;
export declare const Fuel: Icon;
export declare const Fullscreen: Icon;
export declare const FunctionSquare: Icon;
export declare const GalleryHorizontalEnd: Icon;
export declare const GalleryHorizontal: Icon;
export declare const GalleryThumbnails: Icon;
export declare const GalleryVerticalEnd: Icon;
export declare const GalleryVertical: Icon;
export declare const Gamepad2: Icon;
export declare const Gamepad: Icon;
export declare const GanttChartSquare: Icon;
export declare const GanttChart: Icon;
export declare const GaugeCircle: Icon;
export declare const Gauge: Icon;
export declare const Gavel: Icon;
export declare const Gem: Icon;
export declare const Ghost: Icon;
export declare const Gift: Icon;
export declare const GitBranchPlus: Icon;
export declare const GitBranch: Icon;
export declare const GitCommitHorizontal: Icon;
export declare const GitCommitVertical: Icon;
export declare const GitCompareArrows: Icon;
export declare const GitCompare: Icon;
export declare const GitFork: Icon;
export declare const GitGraph: Icon;
export declare const GitMerge: Icon;
export declare const GitPullRequestArrow: Icon;
export declare const GitPullRequestClosed: Icon;
export declare const GitPullRequestCreateArrow: Icon;
export declare const GitPullRequestCreate: Icon;
export declare const GitPullRequestDraft: Icon;
export declare const GitPullRequest: Icon;
export declare const Github: Icon;
export declare const Gitlab: Icon;
export declare const GlassWater: Icon;
export declare const Glasses: Icon;
export declare const Globe2: Icon;
export declare const Globe: Icon;
export declare const Goal: Icon;
export declare const Grab: Icon;
export declare const GraduationCap: Icon;
export declare const Grape: Icon;
export declare const Grid2x2: Icon;
export declare const Grid3x3: Icon;
export declare const GripHorizontal: Icon;
export declare const GripVertical: Icon;
export declare const Grip: Icon;
export declare const Group: Icon;
export declare const Hammer: Icon;
export declare const HandMetal: Icon;
export declare const Hand: Icon;
export declare const HardDriveDownload: Icon;
export declare const HardDriveUpload: Icon;
export declare const HardDrive: Icon;
export declare const HardHat: Icon;
export declare const Hash: Icon;
export declare const Haze: Icon;
export declare const HdmiPort: Icon;
export declare const Heading1: Icon;
export declare const Heading2: Icon;
export declare const Heading3: Icon;
export declare const Heading4: Icon;
export declare const Heading5: Icon;
export declare const Heading6: Icon;
export declare const Heading: Icon;
export declare const Headphones: Icon;
export declare const HeartCrack: Icon;
export declare const HeartHandshake: Icon;
export declare const HeartOff: Icon;
export declare const HeartPulse: Icon;
export declare const Heart: Icon;
export declare const HelpCircle: Icon;
export declare const HelpingHand: Icon;
export declare const Hexagon: Icon;
export declare const Highlighter: Icon;
export declare const History: Icon;
export declare const Home: Icon;
export declare const HopOff: Icon;
export declare const Hop: Icon;
export declare const Hotel: Icon;
export declare const Hourglass: Icon;
export declare const IceCream2: Icon;
export declare const IceCream: Icon;
export declare const ImageDown: Icon;
export declare const ImageMinus: Icon;
export declare const ImageOff: Icon;
export declare const ImagePlus: Icon;
export declare const Image: Icon;
export declare const Import: Icon;
export declare const Inbox: Icon;
export declare const Indent: Icon;
export declare const IndianRupee: Icon;
export declare const Infinity: Icon;
export declare const Info: Icon;
export declare const Instagram: Icon;
export declare const Italic: Icon;
export declare const IterationCcw: Icon;
export declare const IterationCw: Icon;
export declare const JapaneseYen: Icon;
export declare const Joystick: Icon;
export declare const KanbanSquareDashed: Icon;
export declare const KanbanSquare: Icon;
export declare const Kanban: Icon;
export declare const KeyRound: Icon;
export declare const KeySquare: Icon;
export declare const Key: Icon;
export declare const Keyboard: Icon;
export declare const LampCeiling: Icon;
export declare const LampDesk: Icon;
export declare const LampFloor: Icon;
export declare const LampWallDown: Icon;
export declare const LampWallUp: Icon;
export declare const Lamp: Icon;
export declare const LandPlot: Icon;
export declare const Landmark: Icon;
export declare const Languages: Icon;
export declare const Laptop2: Icon;
export declare const Laptop: Icon;
export declare const LassoSelect: Icon;
export declare const Lasso: Icon;
export declare const Laugh: Icon;
export declare const Layers2: Icon;
export declare const Layers3: Icon;
export declare const Layers: Icon;
export declare const LayoutDashboard: Icon;
export declare const LayoutGrid: Icon;
export declare const LayoutList: Icon;
export declare const LayoutPanelLeft: Icon;
export declare const LayoutPanelTop: Icon;
export declare const LayoutTemplate: Icon;
export declare const Layout: Icon;
export declare const Leaf: Icon;
export declare const LeafyGreen: Icon;
export declare const LibraryBig: Icon;
export declare const LibrarySquare: Icon;
export declare const Library: Icon;
export declare const LifeBuoy: Icon;
export declare const Ligature: Icon;
export declare const LightbulbOff: Icon;
export declare const Lightbulb: Icon;
export declare const LineChart: Icon;
export declare const Link2Off: Icon;
export declare const Link2: Icon;
export declare const Link: Icon;
export declare const Linkedin: Icon;
export declare const ListChecks: Icon;
export declare const ListEnd: Icon;
export declare const ListFilter: Icon;
export declare const ListMinus: Icon;
export declare const ListMusic: Icon;
export declare const ListOrdered: Icon;
export declare const ListPlus: Icon;
export declare const ListRestart: Icon;
export declare const ListStart: Icon;
export declare const ListTodo: Icon;
export declare const ListTree: Icon;
export declare const ListVideo: Icon;
export declare const ListX: Icon;
export declare const List: Icon;
export declare const Loader2: Icon;
export declare const Loader: Icon;
export declare const LocateFixed: Icon;
export declare const LocateOff: Icon;
export declare const Locate: Icon;
export declare const LockKeyhole: Icon;
export declare const Lock: Icon;
export declare const LogIn: Icon;
export declare const LogOut: Icon;
export declare const Lollipop: Icon;
export declare const Luggage: Icon;
export declare const MSquare: Icon;
export declare const Magnet: Icon;
export declare const MailCheck: Icon;
export declare const MailMinus: Icon;
export declare const MailOpen: Icon;
export declare const MailPlus: Icon;
export declare const MailQuestion: Icon;
export declare const MailSearch: Icon;
export declare const MailWarning: Icon;
export declare const MailX: Icon;
export declare const Mail: Icon;
export declare const Mailbox: Icon;
export declare const Mails: Icon;
export declare const MapPinOff: Icon;
export declare const MapPin: Icon;
export declare const MapPinned: Icon;
export declare const Map: Icon;
export declare const Martini: Icon;
export declare const Maximize2: Icon;
export declare const Maximize: Icon;
export declare const Medal: Icon;
export declare const MegaphoneOff: Icon;
export declare const Megaphone: Icon;
export declare const Meh: Icon;
export declare const MemoryStick: Icon;
export declare const MenuSquare: Icon;
export declare const Menu: Icon;
export declare const Merge: Icon;
export declare const MessageCircle: Icon;
export declare const MessageSquareDashed: Icon;
export declare const MessageSquarePlus: Icon;
export declare const MessageSquare: Icon;
export declare const MessagesSquare: Icon;
export declare const Mic2: Icon;
export declare const MicOff: Icon;
export declare const Mic: Icon;
export declare const Microscope: Icon;
export declare const Microwave: Icon;
export declare const Milestone: Icon;
export declare const MilkOff: Icon;
export declare const Milk: Icon;
export declare const Minimize2: Icon;
export declare const Minimize: Icon;
export declare const MinusCircle: Icon;
export declare const MinusSquare: Icon;
export declare const Minus: Icon;
export declare const MonitorCheck: Icon;
export declare const MonitorDot: Icon;
export declare const MonitorDown: Icon;
export declare const MonitorOff: Icon;
export declare const MonitorPause: Icon;
export declare const MonitorPlay: Icon;
export declare const MonitorSmartphone: Icon;
export declare const MonitorSpeaker: Icon;
export declare const MonitorStop: Icon;
export declare const MonitorUp: Icon;
export declare const MonitorX: Icon;
export declare const Monitor: Icon;
export declare const MoonStar: Icon;
export declare const Moon: Icon;
export declare const MoreHorizontal: Icon;
export declare const MoreVertical: Icon;
export declare const MountainSnow: Icon;
export declare const Mountain: Icon;
export declare const MousePointer2: Icon;
export declare const MousePointerClick: Icon;
export declare const MousePointerSquareDashed: Icon;
export declare const MousePointerSquare: Icon;
export declare const MousePointer: Icon;
export declare const Mouse: Icon;
export declare const Move3d: Icon;
export declare const MoveDiagonal2: Icon;
export declare const MoveDiagonal: Icon;
export declare const MoveDownLeft: Icon;
export declare const MoveDownRight: Icon;
export declare const MoveDown: Icon;
export declare const MoveHorizontal: Icon;
export declare const MoveLeft: Icon;
export declare const MoveRight: Icon;
export declare const MoveUpLeft: Icon;
export declare const MoveUpRight: Icon;
export declare const MoveUp: Icon;
export declare const MoveVertical: Icon;
export declare const Move: Icon;
export declare const Music2: Icon;
export declare const Music3: Icon;
export declare const Music4: Icon;
export declare const Music: Icon;
export declare const Navigation2Off: Icon;
export declare const Navigation2: Icon;
export declare const NavigationOff: Icon;
export declare const Navigation: Icon;
export declare const Network: Icon;
export declare const Newspaper: Icon;
export declare const Nfc: Icon;
export declare const NutOff: Icon;
export declare const Nut: Icon;
export declare const Octagon: Icon;
export declare const Option: Icon;
export declare const Orbit: Icon;
export declare const Outdent: Icon;
export declare const Package2: Icon;
export declare const PackageCheck: Icon;
export declare const PackageMinus: Icon;
export declare const PackageOpen: Icon;
export declare const PackagePlus: Icon;
export declare const PackageSearch: Icon;
export declare const PackageX: Icon;
export declare const Package: Icon;
export declare const PaintBucket: Icon;
export declare const Paintbrush2: Icon;
export declare const Paintbrush: Icon;
export declare const Palette: Icon;
export declare const Palmtree: Icon;
export declare const PanelBottomClose: Icon;
export declare const PanelBottomInactive: Icon;
export declare const PanelBottomOpen: Icon;
export declare const PanelBottom: Icon;
export declare const PanelLeftClose: Icon;
export declare const PanelLeftInactive: Icon;
export declare const PanelLeftOpen: Icon;
export declare const PanelLeft: Icon;
export declare const PanelRightClose: Icon;
export declare const PanelRightInactive: Icon;
export declare const PanelRightOpen: Icon;
export declare const PanelRight: Icon;
export declare const PanelTopClose: Icon;
export declare const PanelTopInactive: Icon;
export declare const PanelTopOpen: Icon;
export declare const PanelTop: Icon;
export declare const Paperclip: Icon;
export declare const Parentheses: Icon;
export declare const ParkingCircleOff: Icon;
export declare const ParkingCircle: Icon;
export declare const ParkingMeter: Icon;
export declare const ParkingSquareOff: Icon;
export declare const ParkingSquare: Icon;
export declare const PartyPopper: Icon;
export declare const PauseCircle: Icon;
export declare const PauseOctagon: Icon;
export declare const Pause: Icon;
export declare const PawPrint: Icon;
export declare const PcCase: Icon;
export declare const PenLine: Icon;
export declare const PenSquare: Icon;
export declare const PenTool: Icon;
export declare const Pen: Icon;
export declare const PencilLine: Icon;
export declare const PencilRuler: Icon;
export declare const Pencil: Icon;
export declare const Pentagon: Icon;
export declare const PercentCircle: Icon;
export declare const PercentDiamond: Icon;
export declare const PercentSquare: Icon;
export declare const Percent: Icon;
export declare const PersonStanding: Icon;
export declare const PhoneCall: Icon;
export declare const PhoneForwarded: Icon;
export declare const PhoneIncoming: Icon;
export declare const PhoneMissed: Icon;
export declare const PhoneOff: Icon;
export declare const PhoneOutgoing: Icon;
export declare const Phone: Icon;
export declare const PiSquare: Icon;
export declare const Pi: Icon;
export declare const PictureInPicture2: Icon;
export declare const PictureInPicture: Icon;
export declare const PieChart: Icon;
export declare const PiggyBank: Icon;
export declare const PilcrowSquare: Icon;
export declare const Pilcrow: Icon;
export declare const Pill: Icon;
export declare const PinOff: Icon;
export declare const Pin: Icon;
export declare const Pipette: Icon;
export declare const Pizza: Icon;
export declare const PlaneLanding: Icon;
export declare const PlaneTakeoff: Icon;
export declare const Plane: Icon;
export declare const PlayCircle: Icon;
export declare const PlaySquare: Icon;
export declare const Play: Icon;
export declare const Plug2: Icon;
export declare const PlugZap2: Icon;
export declare const PlugZap: Icon;
export declare const Plug: Icon;
export declare const PlusCircle: Icon;
export declare const PlusSquare: Icon;
export declare const Plus: Icon;
export declare const PocketKnife: Icon;
export declare const Pocket: Icon;
export declare const Podcast: Icon;
export declare const Pointer: Icon;
export declare const Popcorn: Icon;
export declare const Popsicle: Icon;
export declare const PoundSterling: Icon;
export declare const PowerCircle: Icon;
export declare const PowerOff: Icon;
export declare const PowerSquare: Icon;
export declare const Power: Icon;
export declare const Presentation: Icon;
export declare const Printer: Icon;
export declare const Projector: Icon;
export declare const Puzzle: Icon;
export declare const Pyramid: Icon;
export declare const QrCode: Icon;
export declare const Quote: Icon;
export declare const Rabbit: Icon;
export declare const Radar: Icon;
export declare const Radiation: Icon;
export declare const RadioReceiver: Icon;
export declare const RadioTower: Icon;
export declare const Radio: Icon;
export declare const Radius: Icon;
export declare const RailSymbol: Icon;
export declare const Rainbow: Icon;
export declare const Rat: Icon;
export declare const Ratio: Icon;
export declare const Receipt: Icon;
export declare const RectangleHorizontal: Icon;
export declare const RectangleVertical: Icon;
export declare const Recycle: Icon;
export declare const Redo2: Icon;
export declare const RedoDot: Icon;
export declare const Redo: Icon;
export declare const RefreshCcwDot: Icon;
export declare const RefreshCcw: Icon;
export declare const RefreshCwOff: Icon;
export declare const RefreshCw: Icon;
export declare const Refrigerator: Icon;
export declare const Regex: Icon;
export declare const RemoveFormatting: Icon;
export declare const Repeat1: Icon;
export declare const Repeat2: Icon;
export declare const Repeat: Icon;
export declare const ReplaceAll: Icon;
export declare const Replace: Icon;
export declare const ReplyAll: Icon;
export declare const Reply: Icon;
export declare const Rewind: Icon;
export declare const Ribbon: Icon;
export declare const Rocket: Icon;
export declare const RockingChair: Icon;
export declare const RollerCoaster: Icon;
export declare const Rotate3d: Icon;
export declare const RotateCcw: Icon;
export declare const RotateCw: Icon;
export declare const RouteOff: Icon;
export declare const Route: Icon;
export declare const Router: Icon;
export declare const Rows: Icon;
export declare const Rss: Icon;
export declare const Ruler: Icon;
export declare const RussianRuble: Icon;
export declare const Sailboat: Icon;
export declare const Salad: Icon;
export declare const Sandwich: Icon;
export declare const SatelliteDish: Icon;
export declare const Satellite: Icon;
export declare const SaveAll: Icon;
export declare const Save: Icon;
export declare const Scale3d: Icon;
export declare const Scale: Icon;
export declare const Scaling: Icon;
export declare const ScanBarcode: Icon;
export declare const ScanEye: Icon;
export declare const ScanFace: Icon;
export declare const ScanLine: Icon;
export declare const ScanSearch: Icon;
export declare const ScanText: Icon;
export declare const Scan: Icon;
export declare const ScatterChart: Icon;
export declare const School2: Icon;
export declare const School: Icon;
export declare const ScissorsLineDashed: Icon;
export declare const ScissorsSquareDashedBottom: Icon;
export declare const ScissorsSquare: Icon;
export declare const Scissors: Icon;
export declare const ScreenShareOff: Icon;
export declare const ScreenShare: Icon;
export declare const ScrollText: Icon;
export declare const Scroll: Icon;
export declare const SearchCheck: Icon;
export declare const SearchCode: Icon;
export declare const SearchSlash: Icon;
export declare const SearchX: Icon;
export declare const Search: Icon;
export declare const SendHorizontal: Icon;
export declare const SendToBack: Icon;
export declare const Send: Icon;
export declare const SeparatorHorizontal: Icon;
export declare const SeparatorVertical: Icon;
export declare const ServerCog: Icon;
export declare const ServerCrash: Icon;
export declare const ServerOff: Icon;
export declare const Server: Icon;
export declare const Settings2: Icon;
export declare const Settings: Icon;
export declare const Shapes: Icon;
export declare const Share2: Icon;
export declare const Share: Icon;
export declare const Sheet: Icon;
export declare const Shell: Icon;
export declare const ShieldAlert: Icon;
export declare const ShieldBan: Icon;
export declare const ShieldCheck: Icon;
export declare const ShieldEllipsis: Icon;
export declare const ShieldHalf: Icon;
export declare const ShieldMinus: Icon;
export declare const ShieldOff: Icon;
export declare const ShieldPlus: Icon;
export declare const ShieldQuestion: Icon;
export declare const ShieldX: Icon;
export declare const Shield: Icon;
export declare const ShipWheel: Icon;
export declare const Ship: Icon;
export declare const Shirt: Icon;
export declare const ShoppingBag: Icon;
export declare const ShoppingBasket: Icon;
export declare const ShoppingCart: Icon;
export declare const Shovel: Icon;
export declare const ShowerHead: Icon;
export declare const Shrink: Icon;
export declare const Shrub: Icon;
export declare const Shuffle: Icon;
export declare const SigmaSquare: Icon;
export declare const Sigma: Icon;
export declare const SignalHigh: Icon;
export declare const SignalLow: Icon;
export declare const SignalMedium: Icon;
export declare const SignalZero: Icon;
export declare const Signal: Icon;
export declare const SignpostBig: Icon;
export declare const Signpost: Icon;
export declare const Siren: Icon;
export declare const SkipBack: Icon;
export declare const SkipForward: Icon;
export declare const Skull: Icon;
export declare const Slack: Icon;
export declare const Slash: Icon;
export declare const Slice: Icon;
export declare const SlidersHorizontal: Icon;
export declare const Sliders: Icon;
export declare const SmartphoneCharging: Icon;
export declare const SmartphoneNfc: Icon;
export declare const Smartphone: Icon;
export declare const SmilePlus: Icon;
export declare const Smile: Icon;
export declare const Snail: Icon;
export declare const Snowflake: Icon;
export declare const Sofa: Icon;
export declare const Soup: Icon;
export declare const Space: Icon;
export declare const Spade: Icon;
export declare const Sparkle: Icon;
export declare const Sparkles: Icon;
export declare const Speaker: Icon;
export declare const Speech: Icon;
export declare const SpellCheck2: Icon;
export declare const SpellCheck: Icon;
export declare const Spline: Icon;
export declare const SplitSquareHorizontal: Icon;
export declare const SplitSquareVertical: Icon;
export declare const Split: Icon;
export declare const SprayCan: Icon;
export declare const Sprout: Icon;
export declare const SquareAsterisk: Icon;
export declare const SquareCode: Icon;
export declare const SquareDashedBottomCode: Icon;
export declare const SquareDashedBottom: Icon;
export declare const SquareDot: Icon;
export declare const SquareEqual: Icon;
export declare const SquareSlash: Icon;
export declare const SquareStack: Icon;
export declare const Square: Icon;
export declare const Squirrel: Icon;
export declare const Stamp: Icon;
export declare const StarHalf: Icon;
export declare const StarOff: Icon;
export declare const Star: Icon;
export declare const StepBack: Icon;
export declare const StepForward: Icon;
export declare const Stethoscope: Icon;
export declare const Sticker: Icon;
export declare const StickyNote: Icon;
export declare const StopCircle: Icon;
export declare const Store: Icon;
export declare const StretchHorizontal: Icon;
export declare const StretchVertical: Icon;
export declare const Strikethrough: Icon;
export declare const Subscript: Icon;
export declare const Subtitles: Icon;
export declare const SunDim: Icon;
export declare const SunMedium: Icon;
export declare const SunMoon: Icon;
export declare const SunSnow: Icon;
export declare const Sun: Icon;
export declare const Sunrise: Icon;
export declare const Sunset: Icon;
export declare const Superscript: Icon;
export declare const SwissFranc: Icon;
export declare const SwitchCamera: Icon;
export declare const Sword: Icon;
export declare const Swords: Icon;
export declare const Syringe: Icon;
export declare const Table2: Icon;
export declare const TableProperties: Icon;
export declare const Table: Icon;
export declare const TabletSmartphone: Icon;
export declare const Tablet: Icon;
export declare const Tablets: Icon;
export declare const Tag: Icon;
export declare const Tags: Icon;
export declare const Tally1: Icon;
export declare const Tally2: Icon;
export declare const Tally3: Icon;
export declare const Tally4: Icon;
export declare const Tally5: Icon;
export declare const Tangent: Icon;
export declare const Target: Icon;
export declare const TentTree: Icon;
export declare const Tent: Icon;
export declare const TerminalSquare: Icon;
export declare const Terminal: Icon;
export declare const TestTube2: Icon;
export declare const TestTube: Icon;
export declare const TestTubes: Icon;
export declare const TextCursorInput: Icon;
export declare const TextCursor: Icon;
export declare const TextQuote: Icon;
export declare const TextSelect: Icon;
export declare const Text: Icon;
export declare const Theater: Icon;
export declare const ThermometerSnowflake: Icon;
export declare const ThermometerSun: Icon;
export declare const Thermometer: Icon;
export declare const ThumbsDown: Icon;
export declare const ThumbsUp: Icon;
export declare const Ticket: Icon;
export declare const TimerOff: Icon;
export declare const TimerReset: Icon;
export declare const Timer: Icon;
export declare const ToggleLeft: Icon;
export declare const ToggleRight: Icon;
export declare const Tornado: Icon;
export declare const Torus: Icon;
export declare const TouchpadOff: Icon;
export declare const Touchpad: Icon;
export declare const TowerControl: Icon;
export declare const ToyBrick: Icon;
export declare const Tractor: Icon;
export declare const TrafficCone: Icon;
export declare const TrainFrontTunnel: Icon;
export declare const TrainFront: Icon;
export declare const TrainTrack: Icon;
export declare const TramFront: Icon;
export declare const Trash2: Icon;
export declare const Trash: Icon;
export declare const TreeDeciduous: Icon;
export declare const TreePine: Icon;
export declare const Trees: Icon;
export declare const Trello: Icon;
export declare const TrendingDown: Icon;
export declare const TrendingUp: Icon;
export declare const TriangleRight: Icon;
export declare const Triangle: Icon;
export declare const Trophy: Icon;
export declare const Truck: Icon;
export declare const Turtle: Icon;
export declare const Tv2: Icon;
export declare const Tv: Icon;
export declare const Twitch: Icon;
export declare const Twitter: Icon;
export declare const Type: Icon;
export declare const UmbrellaOff: Icon;
export declare const Umbrella: Icon;
export declare const Underline: Icon;
export declare const Undo2: Icon;
export declare const UndoDot: Icon;
export declare const Undo: Icon;
export declare const UnfoldHorizontal: Icon;
export declare const UnfoldVertical: Icon;
export declare const Ungroup: Icon;
export declare const Unlink2: Icon;
export declare const Unlink: Icon;
export declare const UnlockKeyhole: Icon;
export declare const Unlock: Icon;
export declare const Unplug: Icon;
export declare const UploadCloud: Icon;
export declare const Upload: Icon;
export declare const Usb: Icon;
export declare const User2: Icon;
export declare const UserCheck2: Icon;
export declare const UserCheck: Icon;
export declare const UserCircle2: Icon;
export declare const UserCircle: Icon;
export declare const UserCog2: Icon;
export declare const UserCog: Icon;
export declare const UserMinus2: Icon;
export declare const UserMinus: Icon;
export declare const UserPlus2: Icon;
export declare const UserPlus: Icon;
export declare const UserSquare2: Icon;
export declare const UserSquare: Icon;
export declare const UserX2: Icon;
export declare const UserX: Icon;
export declare const User: Icon;
export declare const Users2: Icon;
export declare const Users: Icon;
export declare const UtensilsCrossed: Icon;
export declare const Utensils: Icon;
export declare const UtilityPole: Icon;
export declare const Variable: Icon;
export declare const Vegan: Icon;
export declare const VenetianMask: Icon;
export declare const VibrateOff: Icon;
export declare const Vibrate: Icon;
export declare const VideoOff: Icon;
export declare const Video: Icon;
export declare const Videotape: Icon;
export declare const View: Icon;
export declare const Voicemail: Icon;
export declare const Volume1: Icon;
export declare const Volume2: Icon;
export declare const VolumeX: Icon;
export declare const Volume: Icon;
export declare const Vote: Icon;
export declare const Wallet2: Icon;
export declare const WalletCards: Icon;
export declare const Wallet: Icon;
export declare const Wallpaper: Icon;
export declare const Wand2: Icon;
export declare const Wand: Icon;
export declare const Warehouse: Icon;
export declare const Watch: Icon;
export declare const Waves: Icon;
export declare const Waypoints: Icon;
export declare const Webcam: Icon;
export declare const Webhook: Icon;
export declare const Weight: Icon;
export declare const WheatOff: Icon;
export declare const Wheat: Icon;
export declare const WholeWord: Icon;
export declare const WifiOff: Icon;
export declare const Wifi: Icon;
export declare const Wind: Icon;
export declare const WineOff: Icon;
export declare const Wine: Icon;
export declare const Workflow: Icon;
export declare const WrapText: Icon;
export declare const Wrench: Icon;
export declare const XCircle: Icon;
export declare const XOctagon: Icon;
export declare const XSquare: Icon;
export declare const X: Icon;
export declare const Youtube: Icon;
export declare const ZapOff: Icon;
export declare const Zap: Icon;
export declare const ZoomIn: Icon;
export declare const ZoomOut: Icon;



// Generated icon aliases
// Accessibility aliases
export declare const AccessibilityIcon: Icon;
export declare const LucideAccessibility: Icon;

// ActivitySquare aliases
export declare const ActivitySquareIcon: Icon;
export declare const LucideActivitySquare: Icon;

// Activity aliases
export declare const ActivityIcon: Icon;
export declare const LucideActivity: Icon;

// AirVent aliases
export declare const AirVentIcon: Icon;
export declare const LucideAirVent: Icon;

// Airplay aliases
export declare const AirplayIcon: Icon;
export declare const LucideAirplay: Icon;

// AlarmCheck aliases
export declare const AlarmCheckIcon: Icon;
export declare const LucideAlarmCheck: Icon;

// AlarmClockOff aliases
export declare const AlarmClockOffIcon: Icon;
export declare const LucideAlarmClockOff: Icon;

// AlarmClock aliases
export declare const AlarmClockIcon: Icon;
export declare const LucideAlarmClock: Icon;

// AlarmMinus aliases
export declare const AlarmMinusIcon: Icon;
export declare const LucideAlarmMinus: Icon;

// AlarmPlus aliases
export declare const AlarmPlusIcon: Icon;
export declare const LucideAlarmPlus: Icon;

// Album aliases
export declare const AlbumIcon: Icon;
export declare const LucideAlbum: Icon;

// AlertCircle aliases
export declare const AlertCircleIcon: Icon;
export declare const LucideAlertCircle: Icon;

// AlertOctagon aliases
export declare const AlertOctagonIcon: Icon;
export declare const LucideAlertOctagon: Icon;

// AlertTriangle aliases
export declare const AlertTriangleIcon: Icon;
export declare const LucideAlertTriangle: Icon;

// AlignCenterHorizontal aliases
export declare const AlignCenterHorizontalIcon: Icon;
export declare const LucideAlignCenterHorizontal: Icon;

// AlignCenterVertical aliases
export declare const AlignCenterVerticalIcon: Icon;
export declare const LucideAlignCenterVertical: Icon;

// AlignCenter aliases
export declare const AlignCenterIcon: Icon;
export declare const LucideAlignCenter: Icon;

// AlignEndHorizontal aliases
export declare const AlignEndHorizontalIcon: Icon;
export declare const LucideAlignEndHorizontal: Icon;

// AlignEndVertical aliases
export declare const AlignEndVerticalIcon: Icon;
export declare const LucideAlignEndVertical: Icon;

// AlignHorizontalDistributeCenter aliases
export declare const AlignHorizontalDistributeCenterIcon: Icon;
export declare const LucideAlignHorizontalDistributeCenter: Icon;

// AlignHorizontalDistributeEnd aliases
export declare const AlignHorizontalDistributeEndIcon: Icon;
export declare const LucideAlignHorizontalDistributeEnd: Icon;

// AlignHorizontalDistributeStart aliases
export declare const AlignHorizontalDistributeStartIcon: Icon;
export declare const LucideAlignHorizontalDistributeStart: Icon;

// AlignHorizontalJustifyCenter aliases
export declare const AlignHorizontalJustifyCenterIcon: Icon;
export declare const LucideAlignHorizontalJustifyCenter: Icon;

// AlignHorizontalJustifyEnd aliases
export declare const AlignHorizontalJustifyEndIcon: Icon;
export declare const LucideAlignHorizontalJustifyEnd: Icon;

// AlignHorizontalJustifyStart aliases
export declare const AlignHorizontalJustifyStartIcon: Icon;
export declare const LucideAlignHorizontalJustifyStart: Icon;

// AlignHorizontalSpaceAround aliases
export declare const AlignHorizontalSpaceAroundIcon: Icon;
export declare const LucideAlignHorizontalSpaceAround: Icon;

// AlignHorizontalSpaceBetween aliases
export declare const AlignHorizontalSpaceBetweenIcon: Icon;
export declare const LucideAlignHorizontalSpaceBetween: Icon;

// AlignJustify aliases
export declare const AlignJustifyIcon: Icon;
export declare const LucideAlignJustify: Icon;

// AlignLeft aliases
export declare const AlignLeftIcon: Icon;
export declare const LucideAlignLeft: Icon;

// AlignRight aliases
export declare const AlignRightIcon: Icon;
export declare const LucideAlignRight: Icon;

// AlignStartHorizontal aliases
export declare const AlignStartHorizontalIcon: Icon;
export declare const LucideAlignStartHorizontal: Icon;

// AlignStartVertical aliases
export declare const AlignStartVerticalIcon: Icon;
export declare const LucideAlignStartVertical: Icon;

// AlignVerticalDistributeCenter aliases
export declare const AlignVerticalDistributeCenterIcon: Icon;
export declare const LucideAlignVerticalDistributeCenter: Icon;

// AlignVerticalDistributeEnd aliases
export declare const AlignVerticalDistributeEndIcon: Icon;
export declare const LucideAlignVerticalDistributeEnd: Icon;

// AlignVerticalDistributeStart aliases
export declare const AlignVerticalDistributeStartIcon: Icon;
export declare const LucideAlignVerticalDistributeStart: Icon;

// AlignVerticalJustifyCenter aliases
export declare const AlignVerticalJustifyCenterIcon: Icon;
export declare const LucideAlignVerticalJustifyCenter: Icon;

// AlignVerticalJustifyEnd aliases
export declare const AlignVerticalJustifyEndIcon: Icon;
export declare const LucideAlignVerticalJustifyEnd: Icon;

// AlignVerticalJustifyStart aliases
export declare const AlignVerticalJustifyStartIcon: Icon;
export declare const LucideAlignVerticalJustifyStart: Icon;

// AlignVerticalSpaceAround aliases
export declare const AlignVerticalSpaceAroundIcon: Icon;
export declare const LucideAlignVerticalSpaceAround: Icon;

// AlignVerticalSpaceBetween aliases
export declare const AlignVerticalSpaceBetweenIcon: Icon;
export declare const LucideAlignVerticalSpaceBetween: Icon;

// Ampersand aliases
export declare const AmpersandIcon: Icon;
export declare const LucideAmpersand: Icon;

// Ampersands aliases
export declare const AmpersandsIcon: Icon;
export declare const LucideAmpersands: Icon;

// Anchor aliases
export declare const AnchorIcon: Icon;
export declare const LucideAnchor: Icon;

// Angry aliases
export declare const AngryIcon: Icon;
export declare const LucideAngry: Icon;

// Annoyed aliases
export declare const AnnoyedIcon: Icon;
export declare const LucideAnnoyed: Icon;

// Antenna aliases
export declare const AntennaIcon: Icon;
export declare const LucideAntenna: Icon;

// Aperture aliases
export declare const ApertureIcon: Icon;
export declare const LucideAperture: Icon;

// AppWindow aliases
export declare const AppWindowIcon: Icon;
export declare const LucideAppWindow: Icon;

// Apple aliases
export declare const AppleIcon: Icon;
export declare const LucideApple: Icon;

// ArchiveRestore aliases
export declare const ArchiveRestoreIcon: Icon;
export declare const LucideArchiveRestore: Icon;

// ArchiveX aliases
export declare const ArchiveXIcon: Icon;
export declare const LucideArchiveX: Icon;

// Archive aliases
export declare const ArchiveIcon: Icon;
export declare const LucideArchive: Icon;

// AreaChart aliases
export declare const AreaChartIcon: Icon;
export declare const LucideAreaChart: Icon;

// Armchair aliases
export declare const ArmchairIcon: Icon;
export declare const LucideArmchair: Icon;

// ArrowBigDownDash aliases
export declare const ArrowBigDownDashIcon: Icon;
export declare const LucideArrowBigDownDash: Icon;

// ArrowBigDown aliases
export declare const ArrowBigDownIcon: Icon;
export declare const LucideArrowBigDown: Icon;

// ArrowBigLeftDash aliases
export declare const ArrowBigLeftDashIcon: Icon;
export declare const LucideArrowBigLeftDash: Icon;

// ArrowBigLeft aliases
export declare const ArrowBigLeftIcon: Icon;
export declare const LucideArrowBigLeft: Icon;

// ArrowBigRightDash aliases
export declare const ArrowBigRightDashIcon: Icon;
export declare const LucideArrowBigRightDash: Icon;

// ArrowBigRight aliases
export declare const ArrowBigRightIcon: Icon;
export declare const LucideArrowBigRight: Icon;

// ArrowBigUpDash aliases
export declare const ArrowBigUpDashIcon: Icon;
export declare const LucideArrowBigUpDash: Icon;

// ArrowBigUp aliases
export declare const ArrowBigUpIcon: Icon;
export declare const LucideArrowBigUp: Icon;

// ArrowDown01 aliases
export declare const ArrowDown01Icon: Icon;
export declare const LucideArrowDown01: Icon;
export declare const ArrowDown01: Icon;

// ArrowDown10 aliases
export declare const ArrowDown10Icon: Icon;
export declare const LucideArrowDown10: Icon;
export declare const ArrowDown10: Icon;

// ArrowDownAZ aliases
export declare const ArrowDownAZIcon: Icon;
export declare const LucideArrowDownAZ: Icon;
export declare const ArrowDownAz: Icon;

// ArrowDownCircle aliases
export declare const ArrowDownCircleIcon: Icon;
export declare const LucideArrowDownCircle: Icon;

// ArrowDownFromLine aliases
export declare const ArrowDownFromLineIcon: Icon;
export declare const LucideArrowDownFromLine: Icon;

// ArrowDownLeftFromCircle aliases
export declare const ArrowDownLeftFromCircleIcon: Icon;
export declare const LucideArrowDownLeftFromCircle: Icon;

// ArrowDownLeftSquare aliases
export declare const ArrowDownLeftSquareIcon: Icon;
export declare const LucideArrowDownLeftSquare: Icon;

// ArrowDownLeft aliases
export declare const ArrowDownLeftIcon: Icon;
export declare const LucideArrowDownLeft: Icon;

// ArrowDownNarrowWide aliases
export declare const ArrowDownNarrowWideIcon: Icon;
export declare const LucideArrowDownNarrowWide: Icon;

// ArrowDownRightFromCircle aliases
export declare const ArrowDownRightFromCircleIcon: Icon;
export declare const LucideArrowDownRightFromCircle: Icon;

// ArrowDownRightSquare aliases
export declare const ArrowDownRightSquareIcon: Icon;
export declare const LucideArrowDownRightSquare: Icon;

// ArrowDownRight aliases
export declare const ArrowDownRightIcon: Icon;
export declare const LucideArrowDownRight: Icon;

// ArrowDownSquare aliases
export declare const ArrowDownSquareIcon: Icon;
export declare const LucideArrowDownSquare: Icon;

// ArrowDownToDot aliases
export declare const ArrowDownToDotIcon: Icon;
export declare const LucideArrowDownToDot: Icon;

// ArrowDownToLine aliases
export declare const ArrowDownToLineIcon: Icon;
export declare const LucideArrowDownToLine: Icon;

// ArrowDownUp aliases
export declare const ArrowDownUpIcon: Icon;
export declare const LucideArrowDownUp: Icon;

// ArrowDownWideNarrow aliases
export declare const ArrowDownWideNarrowIcon: Icon;
export declare const LucideArrowDownWideNarrow: Icon;
export declare const SortDesc: Icon;

// ArrowDownZA aliases
export declare const ArrowDownZAIcon: Icon;
export declare const LucideArrowDownZA: Icon;
export declare const ArrowDownZa: Icon;

// ArrowDown aliases
export declare const ArrowDownIcon: Icon;
export declare const LucideArrowDown: Icon;

// ArrowLeftCircle aliases
export declare const ArrowLeftCircleIcon: Icon;
export declare const LucideArrowLeftCircle: Icon;

// ArrowLeftFromLine aliases
export declare const ArrowLeftFromLineIcon: Icon;
export declare const LucideArrowLeftFromLine: Icon;

// ArrowLeftRight aliases
export declare const ArrowLeftRightIcon: Icon;
export declare const LucideArrowLeftRight: Icon;

// ArrowLeftSquare aliases
export declare const ArrowLeftSquareIcon: Icon;
export declare const LucideArrowLeftSquare: Icon;

// ArrowLeftToLine aliases
export declare const ArrowLeftToLineIcon: Icon;
export declare const LucideArrowLeftToLine: Icon;

// ArrowLeft aliases
export declare const ArrowLeftIcon: Icon;
export declare const LucideArrowLeft: Icon;

// ArrowRightCircle aliases
export declare const ArrowRightCircleIcon: Icon;
export declare const LucideArrowRightCircle: Icon;

// ArrowRightFromLine aliases
export declare const ArrowRightFromLineIcon: Icon;
export declare const LucideArrowRightFromLine: Icon;

// ArrowRightLeft aliases
export declare const ArrowRightLeftIcon: Icon;
export declare const LucideArrowRightLeft: Icon;

// ArrowRightSquare aliases
export declare const ArrowRightSquareIcon: Icon;
export declare const LucideArrowRightSquare: Icon;

// ArrowRightToLine aliases
export declare const ArrowRightToLineIcon: Icon;
export declare const LucideArrowRightToLine: Icon;

// ArrowRight aliases
export declare const ArrowRightIcon: Icon;
export declare const LucideArrowRight: Icon;

// ArrowUp01 aliases
export declare const ArrowUp01Icon: Icon;
export declare const LucideArrowUp01: Icon;
export declare const ArrowUp01: Icon;

// ArrowUp10 aliases
export declare const ArrowUp10Icon: Icon;
export declare const LucideArrowUp10: Icon;
export declare const ArrowUp10: Icon;

// ArrowUpAZ aliases
export declare const ArrowUpAZIcon: Icon;
export declare const LucideArrowUpAZ: Icon;
export declare const ArrowUpAz: Icon;

// ArrowUpCircle aliases
export declare const ArrowUpCircleIcon: Icon;
export declare const LucideArrowUpCircle: Icon;

// ArrowUpDown aliases
export declare const ArrowUpDownIcon: Icon;
export declare const LucideArrowUpDown: Icon;

// ArrowUpFromDot aliases
export declare const ArrowUpFromDotIcon: Icon;
export declare const LucideArrowUpFromDot: Icon;

// ArrowUpFromLine aliases
export declare const ArrowUpFromLineIcon: Icon;
export declare const LucideArrowUpFromLine: Icon;

// ArrowUpLeftFromCircle aliases
export declare const ArrowUpLeftFromCircleIcon: Icon;
export declare const LucideArrowUpLeftFromCircle: Icon;

// ArrowUpLeftSquare aliases
export declare const ArrowUpLeftSquareIcon: Icon;
export declare const LucideArrowUpLeftSquare: Icon;

// ArrowUpLeft aliases
export declare const ArrowUpLeftIcon: Icon;
export declare const LucideArrowUpLeft: Icon;

// ArrowUpNarrowWide aliases
export declare const ArrowUpNarrowWideIcon: Icon;
export declare const LucideArrowUpNarrowWide: Icon;
export declare const SortAsc: Icon;

// ArrowUpRightFromCircle aliases
export declare const ArrowUpRightFromCircleIcon: Icon;
export declare const LucideArrowUpRightFromCircle: Icon;

// ArrowUpRightSquare aliases
export declare const ArrowUpRightSquareIcon: Icon;
export declare const LucideArrowUpRightSquare: Icon;

// ArrowUpRight aliases
export declare const ArrowUpRightIcon: Icon;
export declare const LucideArrowUpRight: Icon;

// ArrowUpSquare aliases
export declare const ArrowUpSquareIcon: Icon;
export declare const LucideArrowUpSquare: Icon;

// ArrowUpToLine aliases
export declare const ArrowUpToLineIcon: Icon;
export declare const LucideArrowUpToLine: Icon;

// ArrowUpWideNarrow aliases
export declare const ArrowUpWideNarrowIcon: Icon;
export declare const LucideArrowUpWideNarrow: Icon;

// ArrowUpZA aliases
export declare const ArrowUpZAIcon: Icon;
export declare const LucideArrowUpZA: Icon;
export declare const ArrowUpZa: Icon;

// ArrowUp aliases
export declare const ArrowUpIcon: Icon;
export declare const LucideArrowUp: Icon;

// ArrowsUpFromLine aliases
export declare const ArrowsUpFromLineIcon: Icon;
export declare const LucideArrowsUpFromLine: Icon;

// Asterisk aliases
export declare const AsteriskIcon: Icon;
export declare const LucideAsterisk: Icon;

// AtSign aliases
export declare const AtSignIcon: Icon;
export declare const LucideAtSign: Icon;

// Atom aliases
export declare const AtomIcon: Icon;
export declare const LucideAtom: Icon;

// Award aliases
export declare const AwardIcon: Icon;
export declare const LucideAward: Icon;

// Axe aliases
export declare const AxeIcon: Icon;
export declare const LucideAxe: Icon;

// Axis3d aliases
export declare const Axis3dIcon: Icon;
export declare const LucideAxis3d: Icon;
export declare const Axis3D: Icon;

// Baby aliases
export declare const BabyIcon: Icon;
export declare const LucideBaby: Icon;

// Backpack aliases
export declare const BackpackIcon: Icon;
export declare const LucideBackpack: Icon;

// BadgeAlert aliases
export declare const BadgeAlertIcon: Icon;
export declare const LucideBadgeAlert: Icon;

// BadgeCent aliases
export declare const BadgeCentIcon: Icon;
export declare const LucideBadgeCent: Icon;

// BadgeCheck aliases
export declare const BadgeCheckIcon: Icon;
export declare const LucideBadgeCheck: Icon;
export declare const Verified: Icon;

// BadgeDollarSign aliases
export declare const BadgeDollarSignIcon: Icon;
export declare const LucideBadgeDollarSign: Icon;

// BadgeEuro aliases
export declare const BadgeEuroIcon: Icon;
export declare const LucideBadgeEuro: Icon;

// BadgeHelp aliases
export declare const BadgeHelpIcon: Icon;
export declare const LucideBadgeHelp: Icon;

// BadgeIndianRupee aliases
export declare const BadgeIndianRupeeIcon: Icon;
export declare const LucideBadgeIndianRupee: Icon;

// BadgeInfo aliases
export declare const BadgeInfoIcon: Icon;
export declare const LucideBadgeInfo: Icon;

// BadgeJapaneseYen aliases
export declare const BadgeJapaneseYenIcon: Icon;
export declare const LucideBadgeJapaneseYen: Icon;

// BadgeMinus aliases
export declare const BadgeMinusIcon: Icon;
export declare const LucideBadgeMinus: Icon;

// BadgePercent aliases
export declare const BadgePercentIcon: Icon;
export declare const LucideBadgePercent: Icon;

// BadgePlus aliases
export declare const BadgePlusIcon: Icon;
export declare const LucideBadgePlus: Icon;

// BadgePoundSterling aliases
export declare const BadgePoundSterlingIcon: Icon;
export declare const LucideBadgePoundSterling: Icon;

// BadgeRussianRuble aliases
export declare const BadgeRussianRubleIcon: Icon;
export declare const LucideBadgeRussianRuble: Icon;

// BadgeSwissFranc aliases
export declare const BadgeSwissFrancIcon: Icon;
export declare const LucideBadgeSwissFranc: Icon;

// BadgeX aliases
export declare const BadgeXIcon: Icon;
export declare const LucideBadgeX: Icon;

// Badge aliases
export declare const BadgeIcon: Icon;
export declare const LucideBadge: Icon;

// BaggageClaim aliases
export declare const BaggageClaimIcon: Icon;
export declare const LucideBaggageClaim: Icon;

// Ban aliases
export declare const BanIcon: Icon;
export declare const LucideBan: Icon;

// Banana aliases
export declare const BananaIcon: Icon;
export declare const LucideBanana: Icon;

// Banknote aliases
export declare const BanknoteIcon: Icon;
export declare const LucideBanknote: Icon;

// BarChart2 aliases
export declare const BarChart2Icon: Icon;
export declare const LucideBarChart2: Icon;

// BarChart3 aliases
export declare const BarChart3Icon: Icon;
export declare const LucideBarChart3: Icon;

// BarChart4 aliases
export declare const BarChart4Icon: Icon;
export declare const LucideBarChart4: Icon;

// BarChartBig aliases
export declare const BarChartBigIcon: Icon;
export declare const LucideBarChartBig: Icon;

// BarChartHorizontalBig aliases
export declare const BarChartHorizontalBigIcon: Icon;
export declare const LucideBarChartHorizontalBig: Icon;

// BarChartHorizontal aliases
export declare const BarChartHorizontalIcon: Icon;
export declare const LucideBarChartHorizontal: Icon;

// BarChart aliases
export declare const BarChartIcon: Icon;
export declare const LucideBarChart: Icon;

// Barcode aliases
export declare const BarcodeIcon: Icon;
export declare const LucideBarcode: Icon;

// Baseline aliases
export declare const BaselineIcon: Icon;
export declare const LucideBaseline: Icon;

// Bath aliases
export declare const BathIcon: Icon;
export declare const LucideBath: Icon;

// BatteryCharging aliases
export declare const BatteryChargingIcon: Icon;
export declare const LucideBatteryCharging: Icon;

// BatteryFull aliases
export declare const BatteryFullIcon: Icon;
export declare const LucideBatteryFull: Icon;

// BatteryLow aliases
export declare const BatteryLowIcon: Icon;
export declare const LucideBatteryLow: Icon;

// BatteryMedium aliases
export declare const BatteryMediumIcon: Icon;
export declare const LucideBatteryMedium: Icon;

// BatteryWarning aliases
export declare const BatteryWarningIcon: Icon;
export declare const LucideBatteryWarning: Icon;

// Battery aliases
export declare const BatteryIcon: Icon;
export declare const LucideBattery: Icon;

// Beaker aliases
export declare const BeakerIcon: Icon;
export declare const LucideBeaker: Icon;

// BeanOff aliases
export declare const BeanOffIcon: Icon;
export declare const LucideBeanOff: Icon;

// Bean aliases
export declare const BeanIcon: Icon;
export declare const LucideBean: Icon;

// BedDouble aliases
export declare const BedDoubleIcon: Icon;
export declare const LucideBedDouble: Icon;

// BedSingle aliases
export declare const BedSingleIcon: Icon;
export declare const LucideBedSingle: Icon;

// Bed aliases
export declare const BedIcon: Icon;
export declare const LucideBed: Icon;

// Beef aliases
export declare const BeefIcon: Icon;
export declare const LucideBeef: Icon;

// Beer aliases
export declare const BeerIcon: Icon;
export declare const LucideBeer: Icon;

// BellDot aliases
export declare const BellDotIcon: Icon;
export declare const LucideBellDot: Icon;

// BellMinus aliases
export declare const BellMinusIcon: Icon;
export declare const LucideBellMinus: Icon;

// BellOff aliases
export declare const BellOffIcon: Icon;
export declare const LucideBellOff: Icon;

// BellPlus aliases
export declare const BellPlusIcon: Icon;
export declare const LucideBellPlus: Icon;

// BellRing aliases
export declare const BellRingIcon: Icon;
export declare const LucideBellRing: Icon;

// Bell aliases
export declare const BellIcon: Icon;
export declare const LucideBell: Icon;

// Bike aliases
export declare const BikeIcon: Icon;
export declare const LucideBike: Icon;

// Binary aliases
export declare const BinaryIcon: Icon;
export declare const LucideBinary: Icon;

// Biohazard aliases
export declare const BiohazardIcon: Icon;
export declare const LucideBiohazard: Icon;

// Bird aliases
export declare const BirdIcon: Icon;
export declare const LucideBird: Icon;

// Bitcoin aliases
export declare const BitcoinIcon: Icon;
export declare const LucideBitcoin: Icon;

// Blinds aliases
export declare const BlindsIcon: Icon;
export declare const LucideBlinds: Icon;

// Blocks aliases
export declare const BlocksIcon: Icon;
export declare const LucideBlocks: Icon;

// BluetoothConnected aliases
export declare const BluetoothConnectedIcon: Icon;
export declare const LucideBluetoothConnected: Icon;

// BluetoothOff aliases
export declare const BluetoothOffIcon: Icon;
export declare const LucideBluetoothOff: Icon;

// BluetoothSearching aliases
export declare const BluetoothSearchingIcon: Icon;
export declare const LucideBluetoothSearching: Icon;

// Bluetooth aliases
export declare const BluetoothIcon: Icon;
export declare const LucideBluetooth: Icon;

// Bold aliases
export declare const BoldIcon: Icon;
export declare const LucideBold: Icon;

// Bomb aliases
export declare const BombIcon: Icon;
export declare const LucideBomb: Icon;

// Bone aliases
export declare const BoneIcon: Icon;
export declare const LucideBone: Icon;

// BookA aliases
export declare const BookAIcon: Icon;
export declare const LucideBookA: Icon;

// BookAudio aliases
export declare const BookAudioIcon: Icon;
export declare const LucideBookAudio: Icon;

// BookCheck aliases
export declare const BookCheckIcon: Icon;
export declare const LucideBookCheck: Icon;

// BookCopy aliases
export declare const BookCopyIcon: Icon;
export declare const LucideBookCopy: Icon;

// BookDashed aliases
export declare const BookDashedIcon: Icon;
export declare const LucideBookDashed: Icon;
export declare const BookTemplate: Icon;

// BookDown aliases
export declare const BookDownIcon: Icon;
export declare const LucideBookDown: Icon;

// BookHeadphones aliases
export declare const BookHeadphonesIcon: Icon;
export declare const LucideBookHeadphones: Icon;

// BookHeart aliases
export declare const BookHeartIcon: Icon;
export declare const LucideBookHeart: Icon;

// BookImage aliases
export declare const BookImageIcon: Icon;
export declare const LucideBookImage: Icon;

// BookKey aliases
export declare const BookKeyIcon: Icon;
export declare const LucideBookKey: Icon;

// BookLock aliases
export declare const BookLockIcon: Icon;
export declare const LucideBookLock: Icon;

// BookMarked aliases
export declare const BookMarkedIcon: Icon;
export declare const LucideBookMarked: Icon;

// BookMinus aliases
export declare const BookMinusIcon: Icon;
export declare const LucideBookMinus: Icon;

// BookOpenCheck aliases
export declare const BookOpenCheckIcon: Icon;
export declare const LucideBookOpenCheck: Icon;

// BookOpenText aliases
export declare const BookOpenTextIcon: Icon;
export declare const LucideBookOpenText: Icon;

// BookOpen aliases
export declare const BookOpenIcon: Icon;
export declare const LucideBookOpen: Icon;

// BookPlus aliases
export declare const BookPlusIcon: Icon;
export declare const LucideBookPlus: Icon;

// BookText aliases
export declare const BookTextIcon: Icon;
export declare const LucideBookText: Icon;

// BookType aliases
export declare const BookTypeIcon: Icon;
export declare const LucideBookType: Icon;

// BookUp2 aliases
export declare const BookUp2Icon: Icon;
export declare const LucideBookUp2: Icon;

// BookUp aliases
export declare const BookUpIcon: Icon;
export declare const LucideBookUp: Icon;

// BookUser aliases
export declare const BookUserIcon: Icon;
export declare const LucideBookUser: Icon;

// BookX aliases
export declare const BookXIcon: Icon;
export declare const LucideBookX: Icon;

// Book aliases
export declare const BookIcon: Icon;
export declare const LucideBook: Icon;

// BookmarkCheck aliases
export declare const BookmarkCheckIcon: Icon;
export declare const LucideBookmarkCheck: Icon;

// BookmarkMinus aliases
export declare const BookmarkMinusIcon: Icon;
export declare const LucideBookmarkMinus: Icon;

// BookmarkPlus aliases
export declare const BookmarkPlusIcon: Icon;
export declare const LucideBookmarkPlus: Icon;

// BookmarkX aliases
export declare const BookmarkXIcon: Icon;
export declare const LucideBookmarkX: Icon;

// Bookmark aliases
export declare const BookmarkIcon: Icon;
export declare const LucideBookmark: Icon;

// BoomBox aliases
export declare const BoomBoxIcon: Icon;
export declare const LucideBoomBox: Icon;

// Bot aliases
export declare const BotIcon: Icon;
export declare const LucideBot: Icon;

// BoxSelect aliases
export declare const BoxSelectIcon: Icon;
export declare const LucideBoxSelect: Icon;

// Box aliases
export declare const BoxIcon: Icon;
export declare const LucideBox: Icon;

// Boxes aliases
export declare const BoxesIcon: Icon;
export declare const LucideBoxes: Icon;

// Braces aliases
export declare const BracesIcon: Icon;
export declare const LucideBraces: Icon;
export declare const CurlyBraces: Icon;

// Brackets aliases
export declare const BracketsIcon: Icon;
export declare const LucideBrackets: Icon;

// BrainCircuit aliases
export declare const BrainCircuitIcon: Icon;
export declare const LucideBrainCircuit: Icon;

// BrainCog aliases
export declare const BrainCogIcon: Icon;
export declare const LucideBrainCog: Icon;

// Brain aliases
export declare const BrainIcon: Icon;
export declare const LucideBrain: Icon;

// Briefcase aliases
export declare const BriefcaseIcon: Icon;
export declare const LucideBriefcase: Icon;

// BringToFront aliases
export declare const BringToFrontIcon: Icon;
export declare const LucideBringToFront: Icon;

// Brush aliases
export declare const BrushIcon: Icon;
export declare const LucideBrush: Icon;

// BugOff aliases
export declare const BugOffIcon: Icon;
export declare const LucideBugOff: Icon;

// BugPlay aliases
export declare const BugPlayIcon: Icon;
export declare const LucideBugPlay: Icon;

// Bug aliases
export declare const BugIcon: Icon;
export declare const LucideBug: Icon;

// Building2 aliases
export declare const Building2Icon: Icon;
export declare const LucideBuilding2: Icon;

// Building aliases
export declare const BuildingIcon: Icon;
export declare const LucideBuilding: Icon;

// BusFront aliases
export declare const BusFrontIcon: Icon;
export declare const LucideBusFront: Icon;

// Bus aliases
export declare const BusIcon: Icon;
export declare const LucideBus: Icon;

// CableCar aliases
export declare const CableCarIcon: Icon;
export declare const LucideCableCar: Icon;

// Cable aliases
export declare const CableIcon: Icon;
export declare const LucideCable: Icon;

// CakeSlice aliases
export declare const CakeSliceIcon: Icon;
export declare const LucideCakeSlice: Icon;

// Cake aliases
export declare const CakeIcon: Icon;
export declare const LucideCake: Icon;

// Calculator aliases
export declare const CalculatorIcon: Icon;
export declare const LucideCalculator: Icon;

// CalendarCheck2 aliases
export declare const CalendarCheck2Icon: Icon;
export declare const LucideCalendarCheck2: Icon;

// CalendarCheck aliases
export declare const CalendarCheckIcon: Icon;
export declare const LucideCalendarCheck: Icon;

// CalendarClock aliases
export declare const CalendarClockIcon: Icon;
export declare const LucideCalendarClock: Icon;

// CalendarDays aliases
export declare const CalendarDaysIcon: Icon;
export declare const LucideCalendarDays: Icon;

// CalendarHeart aliases
export declare const CalendarHeartIcon: Icon;
export declare const LucideCalendarHeart: Icon;

// CalendarMinus aliases
export declare const CalendarMinusIcon: Icon;
export declare const LucideCalendarMinus: Icon;

// CalendarOff aliases
export declare const CalendarOffIcon: Icon;
export declare const LucideCalendarOff: Icon;

// CalendarPlus aliases
export declare const CalendarPlusIcon: Icon;
export declare const LucideCalendarPlus: Icon;

// CalendarRange aliases
export declare const CalendarRangeIcon: Icon;
export declare const LucideCalendarRange: Icon;

// CalendarSearch aliases
export declare const CalendarSearchIcon: Icon;
export declare const LucideCalendarSearch: Icon;

// CalendarX2 aliases
export declare const CalendarX2Icon: Icon;
export declare const LucideCalendarX2: Icon;

// CalendarX aliases
export declare const CalendarXIcon: Icon;
export declare const LucideCalendarX: Icon;

// Calendar aliases
export declare const CalendarIcon: Icon;
export declare const LucideCalendar: Icon;

// CameraOff aliases
export declare const CameraOffIcon: Icon;
export declare const LucideCameraOff: Icon;

// Camera aliases
export declare const CameraIcon: Icon;
export declare const LucideCamera: Icon;

// CandlestickChart aliases
export declare const CandlestickChartIcon: Icon;
export declare const LucideCandlestickChart: Icon;

// CandyCane aliases
export declare const CandyCaneIcon: Icon;
export declare const LucideCandyCane: Icon;

// CandyOff aliases
export declare const CandyOffIcon: Icon;
export declare const LucideCandyOff: Icon;

// Candy aliases
export declare const CandyIcon: Icon;
export declare const LucideCandy: Icon;

// CarFront aliases
export declare const CarFrontIcon: Icon;
export declare const LucideCarFront: Icon;

// CarTaxiFront aliases
export declare const CarTaxiFrontIcon: Icon;
export declare const LucideCarTaxiFront: Icon;

// Car aliases
export declare const CarIcon: Icon;
export declare const LucideCar: Icon;

// Caravan aliases
export declare const CaravanIcon: Icon;
export declare const LucideCaravan: Icon;

// Carrot aliases
export declare const CarrotIcon: Icon;
export declare const LucideCarrot: Icon;

// CaseLower aliases
export declare const CaseLowerIcon: Icon;
export declare const LucideCaseLower: Icon;

// CaseSensitive aliases
export declare const CaseSensitiveIcon: Icon;
export declare const LucideCaseSensitive: Icon;

// CaseUpper aliases
export declare const CaseUpperIcon: Icon;
export declare const LucideCaseUpper: Icon;

// CassetteTape aliases
export declare const CassetteTapeIcon: Icon;
export declare const LucideCassetteTape: Icon;

// Cast aliases
export declare const CastIcon: Icon;
export declare const LucideCast: Icon;

// Castle aliases
export declare const CastleIcon: Icon;
export declare const LucideCastle: Icon;

// Cat aliases
export declare const CatIcon: Icon;
export declare const LucideCat: Icon;

// CheckCheck aliases
export declare const CheckCheckIcon: Icon;
export declare const LucideCheckCheck: Icon;

// CheckCircle2 aliases
export declare const CheckCircle2Icon: Icon;
export declare const LucideCheckCircle2: Icon;

// CheckCircle aliases
export declare const CheckCircleIcon: Icon;
export declare const LucideCheckCircle: Icon;

// CheckSquare2 aliases
export declare const CheckSquare2Icon: Icon;
export declare const LucideCheckSquare2: Icon;

// CheckSquare aliases
export declare const CheckSquareIcon: Icon;
export declare const LucideCheckSquare: Icon;

// Check aliases
export declare const CheckIcon: Icon;
export declare const LucideCheck: Icon;

// ChefHat aliases
export declare const ChefHatIcon: Icon;
export declare const LucideChefHat: Icon;

// Cherry aliases
export declare const CherryIcon: Icon;
export declare const LucideCherry: Icon;

// ChevronDownCircle aliases
export declare const ChevronDownCircleIcon: Icon;
export declare const LucideChevronDownCircle: Icon;

// ChevronDownSquare aliases
export declare const ChevronDownSquareIcon: Icon;
export declare const LucideChevronDownSquare: Icon;

// ChevronDown aliases
export declare const ChevronDownIcon: Icon;
export declare const LucideChevronDown: Icon;

// ChevronFirst aliases
export declare const ChevronFirstIcon: Icon;
export declare const LucideChevronFirst: Icon;

// ChevronLast aliases
export declare const ChevronLastIcon: Icon;
export declare const LucideChevronLast: Icon;

// ChevronLeftCircle aliases
export declare const ChevronLeftCircleIcon: Icon;
export declare const LucideChevronLeftCircle: Icon;

// ChevronLeftSquare aliases
export declare const ChevronLeftSquareIcon: Icon;
export declare const LucideChevronLeftSquare: Icon;

// ChevronLeft aliases
export declare const ChevronLeftIcon: Icon;
export declare const LucideChevronLeft: Icon;

// ChevronRightCircle aliases
export declare const ChevronRightCircleIcon: Icon;
export declare const LucideChevronRightCircle: Icon;

// ChevronRightSquare aliases
export declare const ChevronRightSquareIcon: Icon;
export declare const LucideChevronRightSquare: Icon;

// ChevronRight aliases
export declare const ChevronRightIcon: Icon;
export declare const LucideChevronRight: Icon;

// ChevronUpCircle aliases
export declare const ChevronUpCircleIcon: Icon;
export declare const LucideChevronUpCircle: Icon;

// ChevronUpSquare aliases
export declare const ChevronUpSquareIcon: Icon;
export declare const LucideChevronUpSquare: Icon;

// ChevronUp aliases
export declare const ChevronUpIcon: Icon;
export declare const LucideChevronUp: Icon;

// ChevronsDownUp aliases
export declare const ChevronsDownUpIcon: Icon;
export declare const LucideChevronsDownUp: Icon;

// ChevronsDown aliases
export declare const ChevronsDownIcon: Icon;
export declare const LucideChevronsDown: Icon;

// ChevronsLeftRight aliases
export declare const ChevronsLeftRightIcon: Icon;
export declare const LucideChevronsLeftRight: Icon;

// ChevronsLeft aliases
export declare const ChevronsLeftIcon: Icon;
export declare const LucideChevronsLeft: Icon;

// ChevronsRightLeft aliases
export declare const ChevronsRightLeftIcon: Icon;
export declare const LucideChevronsRightLeft: Icon;

// ChevronsRight aliases
export declare const ChevronsRightIcon: Icon;
export declare const LucideChevronsRight: Icon;

// ChevronsUpDown aliases
export declare const ChevronsUpDownIcon: Icon;
export declare const LucideChevronsUpDown: Icon;

// ChevronsUp aliases
export declare const ChevronsUpIcon: Icon;
export declare const LucideChevronsUp: Icon;

// Chrome aliases
export declare const ChromeIcon: Icon;
export declare const LucideChrome: Icon;

// Church aliases
export declare const ChurchIcon: Icon;
export declare const LucideChurch: Icon;

// CigaretteOff aliases
export declare const CigaretteOffIcon: Icon;
export declare const LucideCigaretteOff: Icon;

// Cigarette aliases
export declare const CigaretteIcon: Icon;
export declare const LucideCigarette: Icon;

// CircleDashed aliases
export declare const CircleDashedIcon: Icon;
export declare const LucideCircleDashed: Icon;

// CircleDollarSign aliases
export declare const CircleDollarSignIcon: Icon;
export declare const LucideCircleDollarSign: Icon;

// CircleDotDashed aliases
export declare const CircleDotDashedIcon: Icon;
export declare const LucideCircleDotDashed: Icon;

// CircleDot aliases
export declare const CircleDotIcon: Icon;
export declare const LucideCircleDot: Icon;

// CircleEllipsis aliases
export declare const CircleEllipsisIcon: Icon;
export declare const LucideCircleEllipsis: Icon;

// CircleEqual aliases
export declare const CircleEqualIcon: Icon;
export declare const LucideCircleEqual: Icon;

// CircleOff aliases
export declare const CircleOffIcon: Icon;
export declare const LucideCircleOff: Icon;

// CircleSlash2 aliases
export declare const CircleSlash2Icon: Icon;
export declare const LucideCircleSlash2: Icon;
export declare const CircleSlashed: Icon;

// CircleSlash aliases
export declare const CircleSlashIcon: Icon;
export declare const LucideCircleSlash: Icon;

// Circle aliases
export declare const CircleIcon: Icon;
export declare const LucideCircle: Icon;

// CircuitBoard aliases
export declare const CircuitBoardIcon: Icon;
export declare const LucideCircuitBoard: Icon;

// Citrus aliases
export declare const CitrusIcon: Icon;
export declare const LucideCitrus: Icon;

// Clapperboard aliases
export declare const ClapperboardIcon: Icon;
export declare const LucideClapperboard: Icon;

// ClipboardCheck aliases
export declare const ClipboardCheckIcon: Icon;
export declare const LucideClipboardCheck: Icon;

// ClipboardCopy aliases
export declare const ClipboardCopyIcon: Icon;
export declare const LucideClipboardCopy: Icon;

// ClipboardEdit aliases
export declare const ClipboardEditIcon: Icon;
export declare const LucideClipboardEdit: Icon;

// ClipboardList aliases
export declare const ClipboardListIcon: Icon;
export declare const LucideClipboardList: Icon;

// ClipboardPaste aliases
export declare const ClipboardPasteIcon: Icon;
export declare const LucideClipboardPaste: Icon;

// ClipboardSignature aliases
export declare const ClipboardSignatureIcon: Icon;
export declare const LucideClipboardSignature: Icon;

// ClipboardType aliases
export declare const ClipboardTypeIcon: Icon;
export declare const LucideClipboardType: Icon;

// ClipboardX aliases
export declare const ClipboardXIcon: Icon;
export declare const LucideClipboardX: Icon;

// Clipboard aliases
export declare const ClipboardIcon: Icon;
export declare const LucideClipboard: Icon;

// Clock1 aliases
export declare const Clock1Icon: Icon;
export declare const LucideClock1: Icon;

// Clock10 aliases
export declare const Clock10Icon: Icon;
export declare const LucideClock10: Icon;

// Clock11 aliases
export declare const Clock11Icon: Icon;
export declare const LucideClock11: Icon;

// Clock12 aliases
export declare const Clock12Icon: Icon;
export declare const LucideClock12: Icon;

// Clock2 aliases
export declare const Clock2Icon: Icon;
export declare const LucideClock2: Icon;

// Clock3 aliases
export declare const Clock3Icon: Icon;
export declare const LucideClock3: Icon;

// Clock4 aliases
export declare const Clock4Icon: Icon;
export declare const LucideClock4: Icon;

// Clock5 aliases
export declare const Clock5Icon: Icon;
export declare const LucideClock5: Icon;

// Clock6 aliases
export declare const Clock6Icon: Icon;
export declare const LucideClock6: Icon;

// Clock7 aliases
export declare const Clock7Icon: Icon;
export declare const LucideClock7: Icon;

// Clock8 aliases
export declare const Clock8Icon: Icon;
export declare const LucideClock8: Icon;

// Clock9 aliases
export declare const Clock9Icon: Icon;
export declare const LucideClock9: Icon;

// Clock aliases
export declare const ClockIcon: Icon;
export declare const LucideClock: Icon;

// CloudCog aliases
export declare const CloudCogIcon: Icon;
export declare const LucideCloudCog: Icon;

// CloudDrizzle aliases
export declare const CloudDrizzleIcon: Icon;
export declare const LucideCloudDrizzle: Icon;

// CloudFog aliases
export declare const CloudFogIcon: Icon;
export declare const LucideCloudFog: Icon;

// CloudHail aliases
export declare const CloudHailIcon: Icon;
export declare const LucideCloudHail: Icon;

// CloudLightning aliases
export declare const CloudLightningIcon: Icon;
export declare const LucideCloudLightning: Icon;

// CloudMoonRain aliases
export declare const CloudMoonRainIcon: Icon;
export declare const LucideCloudMoonRain: Icon;

// CloudMoon aliases
export declare const CloudMoonIcon: Icon;
export declare const LucideCloudMoon: Icon;

// CloudOff aliases
export declare const CloudOffIcon: Icon;
export declare const LucideCloudOff: Icon;

// CloudRainWind aliases
export declare const CloudRainWindIcon: Icon;
export declare const LucideCloudRainWind: Icon;

// CloudRain aliases
export declare const CloudRainIcon: Icon;
export declare const LucideCloudRain: Icon;

// CloudSnow aliases
export declare const CloudSnowIcon: Icon;
export declare const LucideCloudSnow: Icon;

// CloudSunRain aliases
export declare const CloudSunRainIcon: Icon;
export declare const LucideCloudSunRain: Icon;

// CloudSun aliases
export declare const CloudSunIcon: Icon;
export declare const LucideCloudSun: Icon;

// Cloud aliases
export declare const CloudIcon: Icon;
export declare const LucideCloud: Icon;

// Cloudy aliases
export declare const CloudyIcon: Icon;
export declare const LucideCloudy: Icon;

// Clover aliases
export declare const CloverIcon: Icon;
export declare const LucideClover: Icon;

// Club aliases
export declare const ClubIcon: Icon;
export declare const LucideClub: Icon;

// Code2 aliases
export declare const Code2Icon: Icon;
export declare const LucideCode2: Icon;

// Code aliases
export declare const CodeIcon: Icon;
export declare const LucideCode: Icon;

// Codepen aliases
export declare const CodepenIcon: Icon;
export declare const LucideCodepen: Icon;

// Codesandbox aliases
export declare const CodesandboxIcon: Icon;
export declare const LucideCodesandbox: Icon;

// Coffee aliases
export declare const CoffeeIcon: Icon;
export declare const LucideCoffee: Icon;

// Cog aliases
export declare const CogIcon: Icon;
export declare const LucideCog: Icon;

// Coins aliases
export declare const CoinsIcon: Icon;
export declare const LucideCoins: Icon;

// Columns aliases
export declare const ColumnsIcon: Icon;
export declare const LucideColumns: Icon;

// Combine aliases
export declare const CombineIcon: Icon;
export declare const LucideCombine: Icon;

// Command aliases
export declare const CommandIcon: Icon;
export declare const LucideCommand: Icon;

// Compass aliases
export declare const CompassIcon: Icon;
export declare const LucideCompass: Icon;

// Component aliases
export declare const ComponentIcon: Icon;
export declare const LucideComponent: Icon;

// Computer aliases
export declare const ComputerIcon: Icon;
export declare const LucideComputer: Icon;

// ConciergeBell aliases
export declare const ConciergeBellIcon: Icon;
export declare const LucideConciergeBell: Icon;

// Cone aliases
export declare const ConeIcon: Icon;
export declare const LucideCone: Icon;

// Construction aliases
export declare const ConstructionIcon: Icon;
export declare const LucideConstruction: Icon;

// Contact2 aliases
export declare const Contact2Icon: Icon;
export declare const LucideContact2: Icon;

// Contact aliases
export declare const ContactIcon: Icon;
export declare const LucideContact: Icon;

// Container aliases
export declare const ContainerIcon: Icon;
export declare const LucideContainer: Icon;

// Contrast aliases
export declare const ContrastIcon: Icon;
export declare const LucideContrast: Icon;

// Cookie aliases
export declare const CookieIcon: Icon;
export declare const LucideCookie: Icon;

// CopyCheck aliases
export declare const CopyCheckIcon: Icon;
export declare const LucideCopyCheck: Icon;

// CopyMinus aliases
export declare const CopyMinusIcon: Icon;
export declare const LucideCopyMinus: Icon;

// CopyPlus aliases
export declare const CopyPlusIcon: Icon;
export declare const LucideCopyPlus: Icon;

// CopySlash aliases
export declare const CopySlashIcon: Icon;
export declare const LucideCopySlash: Icon;

// CopyX aliases
export declare const CopyXIcon: Icon;
export declare const LucideCopyX: Icon;

// Copy aliases
export declare const CopyIcon: Icon;
export declare const LucideCopy: Icon;

// Copyleft aliases
export declare const CopyleftIcon: Icon;
export declare const LucideCopyleft: Icon;

// Copyright aliases
export declare const CopyrightIcon: Icon;
export declare const LucideCopyright: Icon;

// CornerDownLeft aliases
export declare const CornerDownLeftIcon: Icon;
export declare const LucideCornerDownLeft: Icon;

// CornerDownRight aliases
export declare const CornerDownRightIcon: Icon;
export declare const LucideCornerDownRight: Icon;

// CornerLeftDown aliases
export declare const CornerLeftDownIcon: Icon;
export declare const LucideCornerLeftDown: Icon;

// CornerLeftUp aliases
export declare const CornerLeftUpIcon: Icon;
export declare const LucideCornerLeftUp: Icon;

// CornerRightDown aliases
export declare const CornerRightDownIcon: Icon;
export declare const LucideCornerRightDown: Icon;

// CornerRightUp aliases
export declare const CornerRightUpIcon: Icon;
export declare const LucideCornerRightUp: Icon;

// CornerUpLeft aliases
export declare const CornerUpLeftIcon: Icon;
export declare const LucideCornerUpLeft: Icon;

// CornerUpRight aliases
export declare const CornerUpRightIcon: Icon;
export declare const LucideCornerUpRight: Icon;

// Cpu aliases
export declare const CpuIcon: Icon;
export declare const LucideCpu: Icon;

// CreativeCommons aliases
export declare const CreativeCommonsIcon: Icon;
export declare const LucideCreativeCommons: Icon;

// CreditCard aliases
export declare const CreditCardIcon: Icon;
export declare const LucideCreditCard: Icon;

// Croissant aliases
export declare const CroissantIcon: Icon;
export declare const LucideCroissant: Icon;

// Crop aliases
export declare const CropIcon: Icon;
export declare const LucideCrop: Icon;

// Cross aliases
export declare const CrossIcon: Icon;
export declare const LucideCross: Icon;

// Crosshair aliases
export declare const CrosshairIcon: Icon;
export declare const LucideCrosshair: Icon;

// Crown aliases
export declare const CrownIcon: Icon;
export declare const LucideCrown: Icon;

// Cuboid aliases
export declare const CuboidIcon: Icon;
export declare const LucideCuboid: Icon;

// CupSoda aliases
export declare const CupSodaIcon: Icon;
export declare const LucideCupSoda: Icon;

// Currency aliases
export declare const CurrencyIcon: Icon;
export declare const LucideCurrency: Icon;

// Cylinder aliases
export declare const CylinderIcon: Icon;
export declare const LucideCylinder: Icon;

// DatabaseBackup aliases
export declare const DatabaseBackupIcon: Icon;
export declare const LucideDatabaseBackup: Icon;

// DatabaseZap aliases
export declare const DatabaseZapIcon: Icon;
export declare const LucideDatabaseZap: Icon;

// Database aliases
export declare const DatabaseIcon: Icon;
export declare const LucideDatabase: Icon;

// Delete aliases
export declare const DeleteIcon: Icon;
export declare const LucideDelete: Icon;

// Dessert aliases
export declare const DessertIcon: Icon;
export declare const LucideDessert: Icon;

// Diameter aliases
export declare const DiameterIcon: Icon;
export declare const LucideDiameter: Icon;

// Diamond aliases
export declare const DiamondIcon: Icon;
export declare const LucideDiamond: Icon;

// Dice1 aliases
export declare const Dice1Icon: Icon;
export declare const LucideDice1: Icon;

// Dice2 aliases
export declare const Dice2Icon: Icon;
export declare const LucideDice2: Icon;

// Dice3 aliases
export declare const Dice3Icon: Icon;
export declare const LucideDice3: Icon;

// Dice4 aliases
export declare const Dice4Icon: Icon;
export declare const LucideDice4: Icon;

// Dice5 aliases
export declare const Dice5Icon: Icon;
export declare const LucideDice5: Icon;

// Dice6 aliases
export declare const Dice6Icon: Icon;
export declare const LucideDice6: Icon;

// Dices aliases
export declare const DicesIcon: Icon;
export declare const LucideDices: Icon;

// Diff aliases
export declare const DiffIcon: Icon;
export declare const LucideDiff: Icon;

// Disc2 aliases
export declare const Disc2Icon: Icon;
export declare const LucideDisc2: Icon;

// Disc3 aliases
export declare const Disc3Icon: Icon;
export declare const LucideDisc3: Icon;

// Disc aliases
export declare const DiscIcon: Icon;
export declare const LucideDisc: Icon;

// DivideCircle aliases
export declare const DivideCircleIcon: Icon;
export declare const LucideDivideCircle: Icon;

// DivideSquare aliases
export declare const DivideSquareIcon: Icon;
export declare const LucideDivideSquare: Icon;

// Divide aliases
export declare const DivideIcon: Icon;
export declare const LucideDivide: Icon;

// DnaOff aliases
export declare const DnaOffIcon: Icon;
export declare const LucideDnaOff: Icon;

// Dna aliases
export declare const DnaIcon: Icon;
export declare const LucideDna: Icon;

// Dog aliases
export declare const DogIcon: Icon;
export declare const LucideDog: Icon;

// DollarSign aliases
export declare const DollarSignIcon: Icon;
export declare const LucideDollarSign: Icon;

// Donut aliases
export declare const DonutIcon: Icon;
export declare const LucideDonut: Icon;

// DoorClosed aliases
export declare const DoorClosedIcon: Icon;
export declare const LucideDoorClosed: Icon;

// DoorOpen aliases
export declare const DoorOpenIcon: Icon;
export declare const LucideDoorOpen: Icon;

// Dot aliases
export declare const DotIcon: Icon;
export declare const LucideDot: Icon;

// DownloadCloud aliases
export declare const DownloadCloudIcon: Icon;
export declare const LucideDownloadCloud: Icon;

// Download aliases
export declare const DownloadIcon: Icon;
export declare const LucideDownload: Icon;

// DraftingCompass aliases
export declare const DraftingCompassIcon: Icon;
export declare const LucideDraftingCompass: Icon;

// Drama aliases
export declare const DramaIcon: Icon;
export declare const LucideDrama: Icon;

// Dribbble aliases
export declare const DribbbleIcon: Icon;
export declare const LucideDribbble: Icon;

// Droplet aliases
export declare const DropletIcon: Icon;
export declare const LucideDroplet: Icon;

// Droplets aliases
export declare const DropletsIcon: Icon;
export declare const LucideDroplets: Icon;

// Drumstick aliases
export declare const DrumstickIcon: Icon;
export declare const LucideDrumstick: Icon;

// Dumbbell aliases
export declare const DumbbellIcon: Icon;
export declare const LucideDumbbell: Icon;

// EarOff aliases
export declare const EarOffIcon: Icon;
export declare const LucideEarOff: Icon;

// Ear aliases
export declare const EarIcon: Icon;
export declare const LucideEar: Icon;

// EggFried aliases
export declare const EggFriedIcon: Icon;
export declare const LucideEggFried: Icon;

// EggOff aliases
export declare const EggOffIcon: Icon;
export declare const LucideEggOff: Icon;

// Egg aliases
export declare const EggIcon: Icon;
export declare const LucideEgg: Icon;

// EqualNot aliases
export declare const EqualNotIcon: Icon;
export declare const LucideEqualNot: Icon;

// Equal aliases
export declare const EqualIcon: Icon;
export declare const LucideEqual: Icon;

// Eraser aliases
export declare const EraserIcon: Icon;
export declare const LucideEraser: Icon;

// Euro aliases
export declare const EuroIcon: Icon;
export declare const LucideEuro: Icon;

// Expand aliases
export declare const ExpandIcon: Icon;
export declare const LucideExpand: Icon;

// ExternalLink aliases
export declare const ExternalLinkIcon: Icon;
export declare const LucideExternalLink: Icon;

// EyeOff aliases
export declare const EyeOffIcon: Icon;
export declare const LucideEyeOff: Icon;

// Eye aliases
export declare const EyeIcon: Icon;
export declare const LucideEye: Icon;

// Facebook aliases
export declare const FacebookIcon: Icon;
export declare const LucideFacebook: Icon;

// Factory aliases
export declare const FactoryIcon: Icon;
export declare const LucideFactory: Icon;

// Fan aliases
export declare const FanIcon: Icon;
export declare const LucideFan: Icon;

// FastForward aliases
export declare const FastForwardIcon: Icon;
export declare const LucideFastForward: Icon;

// Feather aliases
export declare const FeatherIcon: Icon;
export declare const LucideFeather: Icon;

// FerrisWheel aliases
export declare const FerrisWheelIcon: Icon;
export declare const LucideFerrisWheel: Icon;

// Figma aliases
export declare const FigmaIcon: Icon;
export declare const LucideFigma: Icon;

// FileArchive aliases
export declare const FileArchiveIcon: Icon;
export declare const LucideFileArchive: Icon;

// FileAudio2 aliases
export declare const FileAudio2Icon: Icon;
export declare const LucideFileAudio2: Icon;

// FileAudio aliases
export declare const FileAudioIcon: Icon;
export declare const LucideFileAudio: Icon;

// FileAxis3d aliases
export declare const FileAxis3dIcon: Icon;
export declare const LucideFileAxis3d: Icon;
export declare const FileAxis3D: Icon;

// FileBadge2 aliases
export declare const FileBadge2Icon: Icon;
export declare const LucideFileBadge2: Icon;

// FileBadge aliases
export declare const FileBadgeIcon: Icon;
export declare const LucideFileBadge: Icon;

// FileBarChart2 aliases
export declare const FileBarChart2Icon: Icon;
export declare const LucideFileBarChart2: Icon;

// FileBarChart aliases
export declare const FileBarChartIcon: Icon;
export declare const LucideFileBarChart: Icon;

// FileBox aliases
export declare const FileBoxIcon: Icon;
export declare const LucideFileBox: Icon;

// FileCheck2 aliases
export declare const FileCheck2Icon: Icon;
export declare const LucideFileCheck2: Icon;

// FileCheck aliases
export declare const FileCheckIcon: Icon;
export declare const LucideFileCheck: Icon;

// FileClock aliases
export declare const FileClockIcon: Icon;
export declare const LucideFileClock: Icon;

// FileCode2 aliases
export declare const FileCode2Icon: Icon;
export declare const LucideFileCode2: Icon;

// FileCode aliases
export declare const FileCodeIcon: Icon;
export declare const LucideFileCode: Icon;

// FileCog aliases
export declare const FileCogIcon: Icon;
export declare const LucideFileCog: Icon;
export declare const FileCog2: Icon;

// FileDiff aliases
export declare const FileDiffIcon: Icon;
export declare const LucideFileDiff: Icon;

// FileDigit aliases
export declare const FileDigitIcon: Icon;
export declare const LucideFileDigit: Icon;

// FileDown aliases
export declare const FileDownIcon: Icon;
export declare const LucideFileDown: Icon;

// FileEdit aliases
export declare const FileEditIcon: Icon;
export declare const LucideFileEdit: Icon;

// FileHeart aliases
export declare const FileHeartIcon: Icon;
export declare const LucideFileHeart: Icon;

// FileImage aliases
export declare const FileImageIcon: Icon;
export declare const LucideFileImage: Icon;

// FileInput aliases
export declare const FileInputIcon: Icon;
export declare const LucideFileInput: Icon;

// FileJson2 aliases
export declare const FileJson2Icon: Icon;
export declare const LucideFileJson2: Icon;

// FileJson aliases
export declare const FileJsonIcon: Icon;
export declare const LucideFileJson: Icon;

// FileKey2 aliases
export declare const FileKey2Icon: Icon;
export declare const LucideFileKey2: Icon;

// FileKey aliases
export declare const FileKeyIcon: Icon;
export declare const LucideFileKey: Icon;

// FileLineChart aliases
export declare const FileLineChartIcon: Icon;
export declare const LucideFileLineChart: Icon;

// FileLock2 aliases
export declare const FileLock2Icon: Icon;
export declare const LucideFileLock2: Icon;

// FileLock aliases
export declare const FileLockIcon: Icon;
export declare const LucideFileLock: Icon;

// FileMinus2 aliases
export declare const FileMinus2Icon: Icon;
export declare const LucideFileMinus2: Icon;

// FileMinus aliases
export declare const FileMinusIcon: Icon;
export declare const LucideFileMinus: Icon;

// FileOutput aliases
export declare const FileOutputIcon: Icon;
export declare const LucideFileOutput: Icon;

// FilePieChart aliases
export declare const FilePieChartIcon: Icon;
export declare const LucideFilePieChart: Icon;

// FilePlus2 aliases
export declare const FilePlus2Icon: Icon;
export declare const LucideFilePlus2: Icon;

// FilePlus aliases
export declare const FilePlusIcon: Icon;
export declare const LucideFilePlus: Icon;

// FileQuestion aliases
export declare const FileQuestionIcon: Icon;
export declare const LucideFileQuestion: Icon;

// FileScan aliases
export declare const FileScanIcon: Icon;
export declare const LucideFileScan: Icon;

// FileSearch2 aliases
export declare const FileSearch2Icon: Icon;
export declare const LucideFileSearch2: Icon;

// FileSearch aliases
export declare const FileSearchIcon: Icon;
export declare const LucideFileSearch: Icon;

// FileSignature aliases
export declare const FileSignatureIcon: Icon;
export declare const LucideFileSignature: Icon;

// FileSpreadsheet aliases
export declare const FileSpreadsheetIcon: Icon;
export declare const LucideFileSpreadsheet: Icon;

// FileStack aliases
export declare const FileStackIcon: Icon;
export declare const LucideFileStack: Icon;

// FileSymlink aliases
export declare const FileSymlinkIcon: Icon;
export declare const LucideFileSymlink: Icon;

// FileTerminal aliases
export declare const FileTerminalIcon: Icon;
export declare const LucideFileTerminal: Icon;

// FileText aliases
export declare const FileTextIcon: Icon;
export declare const LucideFileText: Icon;

// FileType2 aliases
export declare const FileType2Icon: Icon;
export declare const LucideFileType2: Icon;

// FileType aliases
export declare const FileTypeIcon: Icon;
export declare const LucideFileType: Icon;

// FileUp aliases
export declare const FileUpIcon: Icon;
export declare const LucideFileUp: Icon;

// FileVideo2 aliases
export declare const FileVideo2Icon: Icon;
export declare const LucideFileVideo2: Icon;

// FileVideo aliases
export declare const FileVideoIcon: Icon;
export declare const LucideFileVideo: Icon;

// FileVolume2 aliases
export declare const FileVolume2Icon: Icon;
export declare const LucideFileVolume2: Icon;

// FileVolume aliases
export declare const FileVolumeIcon: Icon;
export declare const LucideFileVolume: Icon;

// FileWarning aliases
export declare const FileWarningIcon: Icon;
export declare const LucideFileWarning: Icon;

// FileX2 aliases
export declare const FileX2Icon: Icon;
export declare const LucideFileX2: Icon;

// FileX aliases
export declare const FileXIcon: Icon;
export declare const LucideFileX: Icon;

// File aliases
export declare const FileIcon: Icon;
export declare const LucideFile: Icon;

// Files aliases
export declare const FilesIcon: Icon;
export declare const LucideFiles: Icon;

// Film aliases
export declare const FilmIcon: Icon;
export declare const LucideFilm: Icon;

// FilterX aliases
export declare const FilterXIcon: Icon;
export declare const LucideFilterX: Icon;

// Filter aliases
export declare const FilterIcon: Icon;
export declare const LucideFilter: Icon;

// Fingerprint aliases
export declare const FingerprintIcon: Icon;
export declare const LucideFingerprint: Icon;

// FishOff aliases
export declare const FishOffIcon: Icon;
export declare const LucideFishOff: Icon;

// FishSymbol aliases
export declare const FishSymbolIcon: Icon;
export declare const LucideFishSymbol: Icon;

// Fish aliases
export declare const FishIcon: Icon;
export declare const LucideFish: Icon;

// FlagOff aliases
export declare const FlagOffIcon: Icon;
export declare const LucideFlagOff: Icon;

// FlagTriangleLeft aliases
export declare const FlagTriangleLeftIcon: Icon;
export declare const LucideFlagTriangleLeft: Icon;

// FlagTriangleRight aliases
export declare const FlagTriangleRightIcon: Icon;
export declare const LucideFlagTriangleRight: Icon;

// Flag aliases
export declare const FlagIcon: Icon;
export declare const LucideFlag: Icon;

// FlameKindling aliases
export declare const FlameKindlingIcon: Icon;
export declare const LucideFlameKindling: Icon;

// Flame aliases
export declare const FlameIcon: Icon;
export declare const LucideFlame: Icon;

// FlashlightOff aliases
export declare const FlashlightOffIcon: Icon;
export declare const LucideFlashlightOff: Icon;

// Flashlight aliases
export declare const FlashlightIcon: Icon;
export declare const LucideFlashlight: Icon;

// FlaskConicalOff aliases
export declare const FlaskConicalOffIcon: Icon;
export declare const LucideFlaskConicalOff: Icon;

// FlaskConical aliases
export declare const FlaskConicalIcon: Icon;
export declare const LucideFlaskConical: Icon;

// FlaskRound aliases
export declare const FlaskRoundIcon: Icon;
export declare const LucideFlaskRound: Icon;

// FlipHorizontal2 aliases
export declare const FlipHorizontal2Icon: Icon;
export declare const LucideFlipHorizontal2: Icon;

// FlipHorizontal aliases
export declare const FlipHorizontalIcon: Icon;
export declare const LucideFlipHorizontal: Icon;

// FlipVertical2 aliases
export declare const FlipVertical2Icon: Icon;
export declare const LucideFlipVertical2: Icon;

// FlipVertical aliases
export declare const FlipVerticalIcon: Icon;
export declare const LucideFlipVertical: Icon;

// Flower2 aliases
export declare const Flower2Icon: Icon;
export declare const LucideFlower2: Icon;

// Flower aliases
export declare const FlowerIcon: Icon;
export declare const LucideFlower: Icon;

// Focus aliases
export declare const FocusIcon: Icon;
export declare const LucideFocus: Icon;

// FoldHorizontal aliases
export declare const FoldHorizontalIcon: Icon;
export declare const LucideFoldHorizontal: Icon;

// FoldVertical aliases
export declare const FoldVerticalIcon: Icon;
export declare const LucideFoldVertical: Icon;

// FolderArchive aliases
export declare const FolderArchiveIcon: Icon;
export declare const LucideFolderArchive: Icon;

// FolderCheck aliases
export declare const FolderCheckIcon: Icon;
export declare const LucideFolderCheck: Icon;

// FolderClock aliases
export declare const FolderClockIcon: Icon;
export declare const LucideFolderClock: Icon;

// FolderClosed aliases
export declare const FolderClosedIcon: Icon;
export declare const LucideFolderClosed: Icon;

// FolderCog aliases
export declare const FolderCogIcon: Icon;
export declare const LucideFolderCog: Icon;
export declare const FolderCog2: Icon;

// FolderDot aliases
export declare const FolderDotIcon: Icon;
export declare const LucideFolderDot: Icon;

// FolderDown aliases
export declare const FolderDownIcon: Icon;
export declare const LucideFolderDown: Icon;

// FolderEdit aliases
export declare const FolderEditIcon: Icon;
export declare const LucideFolderEdit: Icon;

// FolderGit2 aliases
export declare const FolderGit2Icon: Icon;
export declare const LucideFolderGit2: Icon;

// FolderGit aliases
export declare const FolderGitIcon: Icon;
export declare const LucideFolderGit: Icon;

// FolderHeart aliases
export declare const FolderHeartIcon: Icon;
export declare const LucideFolderHeart: Icon;

// FolderInput aliases
export declare const FolderInputIcon: Icon;
export declare const LucideFolderInput: Icon;

// FolderKanban aliases
export declare const FolderKanbanIcon: Icon;
export declare const LucideFolderKanban: Icon;

// FolderKey aliases
export declare const FolderKeyIcon: Icon;
export declare const LucideFolderKey: Icon;

// FolderLock aliases
export declare const FolderLockIcon: Icon;
export declare const LucideFolderLock: Icon;

// FolderMinus aliases
export declare const FolderMinusIcon: Icon;
export declare const LucideFolderMinus: Icon;

// FolderOpenDot aliases
export declare const FolderOpenDotIcon: Icon;
export declare const LucideFolderOpenDot: Icon;

// FolderOpen aliases
export declare const FolderOpenIcon: Icon;
export declare const LucideFolderOpen: Icon;

// FolderOutput aliases
export declare const FolderOutputIcon: Icon;
export declare const LucideFolderOutput: Icon;

// FolderPlus aliases
export declare const FolderPlusIcon: Icon;
export declare const LucideFolderPlus: Icon;

// FolderRoot aliases
export declare const FolderRootIcon: Icon;
export declare const LucideFolderRoot: Icon;

// FolderSearch2 aliases
export declare const FolderSearch2Icon: Icon;
export declare const LucideFolderSearch2: Icon;

// FolderSearch aliases
export declare const FolderSearchIcon: Icon;
export declare const LucideFolderSearch: Icon;

// FolderSymlink aliases
export declare const FolderSymlinkIcon: Icon;
export declare const LucideFolderSymlink: Icon;

// FolderSync aliases
export declare const FolderSyncIcon: Icon;
export declare const LucideFolderSync: Icon;

// FolderTree aliases
export declare const FolderTreeIcon: Icon;
export declare const LucideFolderTree: Icon;

// FolderUp aliases
export declare const FolderUpIcon: Icon;
export declare const LucideFolderUp: Icon;

// FolderX aliases
export declare const FolderXIcon: Icon;
export declare const LucideFolderX: Icon;

// Folder aliases
export declare const FolderIcon: Icon;
export declare const LucideFolder: Icon;

// Folders aliases
export declare const FoldersIcon: Icon;
export declare const LucideFolders: Icon;

// Footprints aliases
export declare const FootprintsIcon: Icon;
export declare const LucideFootprints: Icon;

// Forklift aliases
export declare const ForkliftIcon: Icon;
export declare const LucideForklift: Icon;

// FormInput aliases
export declare const FormInputIcon: Icon;
export declare const LucideFormInput: Icon;

// Forward aliases
export declare const ForwardIcon: Icon;
export declare const LucideForward: Icon;

// Frame aliases
export declare const FrameIcon: Icon;
export declare const LucideFrame: Icon;

// Framer aliases
export declare const FramerIcon: Icon;
export declare const LucideFramer: Icon;

// Frown aliases
export declare const FrownIcon: Icon;
export declare const LucideFrown: Icon;

// Fuel aliases
export declare const FuelIcon: Icon;
export declare const LucideFuel: Icon;

// Fullscreen aliases
export declare const FullscreenIcon: Icon;
export declare const LucideFullscreen: Icon;

// FunctionSquare aliases
export declare const FunctionSquareIcon: Icon;
export declare const LucideFunctionSquare: Icon;

// GalleryHorizontalEnd aliases
export declare const GalleryHorizontalEndIcon: Icon;
export declare const LucideGalleryHorizontalEnd: Icon;

// GalleryHorizontal aliases
export declare const GalleryHorizontalIcon: Icon;
export declare const LucideGalleryHorizontal: Icon;

// GalleryThumbnails aliases
export declare const GalleryThumbnailsIcon: Icon;
export declare const LucideGalleryThumbnails: Icon;

// GalleryVerticalEnd aliases
export declare const GalleryVerticalEndIcon: Icon;
export declare const LucideGalleryVerticalEnd: Icon;

// GalleryVertical aliases
export declare const GalleryVerticalIcon: Icon;
export declare const LucideGalleryVertical: Icon;

// Gamepad2 aliases
export declare const Gamepad2Icon: Icon;
export declare const LucideGamepad2: Icon;

// Gamepad aliases
export declare const GamepadIcon: Icon;
export declare const LucideGamepad: Icon;

// GanttChartSquare aliases
export declare const GanttChartSquareIcon: Icon;
export declare const LucideGanttChartSquare: Icon;
export declare const SquareGantt: Icon;

// GanttChart aliases
export declare const GanttChartIcon: Icon;
export declare const LucideGanttChart: Icon;

// GaugeCircle aliases
export declare const GaugeCircleIcon: Icon;
export declare const LucideGaugeCircle: Icon;

// Gauge aliases
export declare const GaugeIcon: Icon;
export declare const LucideGauge: Icon;

// Gavel aliases
export declare const GavelIcon: Icon;
export declare const LucideGavel: Icon;

// Gem aliases
export declare const GemIcon: Icon;
export declare const LucideGem: Icon;

// Ghost aliases
export declare const GhostIcon: Icon;
export declare const LucideGhost: Icon;

// Gift aliases
export declare const GiftIcon: Icon;
export declare const LucideGift: Icon;

// GitBranchPlus aliases
export declare const GitBranchPlusIcon: Icon;
export declare const LucideGitBranchPlus: Icon;

// GitBranch aliases
export declare const GitBranchIcon: Icon;
export declare const LucideGitBranch: Icon;

// GitCommitHorizontal aliases
export declare const GitCommitHorizontalIcon: Icon;
export declare const LucideGitCommitHorizontal: Icon;
export declare const GitCommit: Icon;

// GitCommitVertical aliases
export declare const GitCommitVerticalIcon: Icon;
export declare const LucideGitCommitVertical: Icon;

// GitCompareArrows aliases
export declare const GitCompareArrowsIcon: Icon;
export declare const LucideGitCompareArrows: Icon;

// GitCompare aliases
export declare const GitCompareIcon: Icon;
export declare const LucideGitCompare: Icon;

// GitFork aliases
export declare const GitForkIcon: Icon;
export declare const LucideGitFork: Icon;

// GitGraph aliases
export declare const GitGraphIcon: Icon;
export declare const LucideGitGraph: Icon;

// GitMerge aliases
export declare const GitMergeIcon: Icon;
export declare const LucideGitMerge: Icon;

// GitPullRequestArrow aliases
export declare const GitPullRequestArrowIcon: Icon;
export declare const LucideGitPullRequestArrow: Icon;

// GitPullRequestClosed aliases
export declare const GitPullRequestClosedIcon: Icon;
export declare const LucideGitPullRequestClosed: Icon;

// GitPullRequestCreateArrow aliases
export declare const GitPullRequestCreateArrowIcon: Icon;
export declare const LucideGitPullRequestCreateArrow: Icon;

// GitPullRequestCreate aliases
export declare const GitPullRequestCreateIcon: Icon;
export declare const LucideGitPullRequestCreate: Icon;

// GitPullRequestDraft aliases
export declare const GitPullRequestDraftIcon: Icon;
export declare const LucideGitPullRequestDraft: Icon;

// GitPullRequest aliases
export declare const GitPullRequestIcon: Icon;
export declare const LucideGitPullRequest: Icon;

// Github aliases
export declare const GithubIcon: Icon;
export declare const LucideGithub: Icon;

// Gitlab aliases
export declare const GitlabIcon: Icon;
export declare const LucideGitlab: Icon;

// GlassWater aliases
export declare const GlassWaterIcon: Icon;
export declare const LucideGlassWater: Icon;

// Glasses aliases
export declare const GlassesIcon: Icon;
export declare const LucideGlasses: Icon;

// Globe2 aliases
export declare const Globe2Icon: Icon;
export declare const LucideGlobe2: Icon;

// Globe aliases
export declare const GlobeIcon: Icon;
export declare const LucideGlobe: Icon;

// Goal aliases
export declare const GoalIcon: Icon;
export declare const LucideGoal: Icon;

// Grab aliases
export declare const GrabIcon: Icon;
export declare const LucideGrab: Icon;

// GraduationCap aliases
export declare const GraduationCapIcon: Icon;
export declare const LucideGraduationCap: Icon;

// Grape aliases
export declare const GrapeIcon: Icon;
export declare const LucideGrape: Icon;

// Grid2x2 aliases
export declare const Grid2x2Icon: Icon;
export declare const LucideGrid2x2: Icon;
export declare const Grid2X2: Icon;

// Grid3x3 aliases
export declare const Grid3x3Icon: Icon;
export declare const LucideGrid3x3: Icon;
export declare const Grid: Icon;
export declare const Grid3X3: Icon;

// GripHorizontal aliases
export declare const GripHorizontalIcon: Icon;
export declare const LucideGripHorizontal: Icon;

// GripVertical aliases
export declare const GripVerticalIcon: Icon;
export declare const LucideGripVertical: Icon;

// Grip aliases
export declare const GripIcon: Icon;
export declare const LucideGrip: Icon;

// Group aliases
export declare const GroupIcon: Icon;
export declare const LucideGroup: Icon;

// Hammer aliases
export declare const HammerIcon: Icon;
export declare const LucideHammer: Icon;

// HandMetal aliases
export declare const HandMetalIcon: Icon;
export declare const LucideHandMetal: Icon;

// Hand aliases
export declare const HandIcon: Icon;
export declare const LucideHand: Icon;

// HardDriveDownload aliases
export declare const HardDriveDownloadIcon: Icon;
export declare const LucideHardDriveDownload: Icon;

// HardDriveUpload aliases
export declare const HardDriveUploadIcon: Icon;
export declare const LucideHardDriveUpload: Icon;

// HardDrive aliases
export declare const HardDriveIcon: Icon;
export declare const LucideHardDrive: Icon;

// HardHat aliases
export declare const HardHatIcon: Icon;
export declare const LucideHardHat: Icon;

// Hash aliases
export declare const HashIcon: Icon;
export declare const LucideHash: Icon;

// Haze aliases
export declare const HazeIcon: Icon;
export declare const LucideHaze: Icon;

// HdmiPort aliases
export declare const HdmiPortIcon: Icon;
export declare const LucideHdmiPort: Icon;

// Heading1 aliases
export declare const Heading1Icon: Icon;
export declare const LucideHeading1: Icon;

// Heading2 aliases
export declare const Heading2Icon: Icon;
export declare const LucideHeading2: Icon;

// Heading3 aliases
export declare const Heading3Icon: Icon;
export declare const LucideHeading3: Icon;

// Heading4 aliases
export declare const Heading4Icon: Icon;
export declare const LucideHeading4: Icon;

// Heading5 aliases
export declare const Heading5Icon: Icon;
export declare const LucideHeading5: Icon;

// Heading6 aliases
export declare const Heading6Icon: Icon;
export declare const LucideHeading6: Icon;

// Heading aliases
export declare const HeadingIcon: Icon;
export declare const LucideHeading: Icon;

// Headphones aliases
export declare const HeadphonesIcon: Icon;
export declare const LucideHeadphones: Icon;

// HeartCrack aliases
export declare const HeartCrackIcon: Icon;
export declare const LucideHeartCrack: Icon;

// HeartHandshake aliases
export declare const HeartHandshakeIcon: Icon;
export declare const LucideHeartHandshake: Icon;

// HeartOff aliases
export declare const HeartOffIcon: Icon;
export declare const LucideHeartOff: Icon;

// HeartPulse aliases
export declare const HeartPulseIcon: Icon;
export declare const LucideHeartPulse: Icon;

// Heart aliases
export declare const HeartIcon: Icon;
export declare const LucideHeart: Icon;

// HelpCircle aliases
export declare const HelpCircleIcon: Icon;
export declare const LucideHelpCircle: Icon;

// HelpingHand aliases
export declare const HelpingHandIcon: Icon;
export declare const LucideHelpingHand: Icon;

// Hexagon aliases
export declare const HexagonIcon: Icon;
export declare const LucideHexagon: Icon;

// Highlighter aliases
export declare const HighlighterIcon: Icon;
export declare const LucideHighlighter: Icon;

// History aliases
export declare const HistoryIcon: Icon;
export declare const LucideHistory: Icon;

// Home aliases
export declare const HomeIcon: Icon;
export declare const LucideHome: Icon;

// HopOff aliases
export declare const HopOffIcon: Icon;
export declare const LucideHopOff: Icon;

// Hop aliases
export declare const HopIcon: Icon;
export declare const LucideHop: Icon;

// Hotel aliases
export declare const HotelIcon: Icon;
export declare const LucideHotel: Icon;

// Hourglass aliases
export declare const HourglassIcon: Icon;
export declare const LucideHourglass: Icon;

// IceCream2 aliases
export declare const IceCream2Icon: Icon;
export declare const LucideIceCream2: Icon;

// IceCream aliases
export declare const IceCreamIcon: Icon;
export declare const LucideIceCream: Icon;

// ImageDown aliases
export declare const ImageDownIcon: Icon;
export declare const LucideImageDown: Icon;

// ImageMinus aliases
export declare const ImageMinusIcon: Icon;
export declare const LucideImageMinus: Icon;

// ImageOff aliases
export declare const ImageOffIcon: Icon;
export declare const LucideImageOff: Icon;

// ImagePlus aliases
export declare const ImagePlusIcon: Icon;
export declare const LucideImagePlus: Icon;

// Image aliases
export declare const ImageIcon: Icon;
export declare const LucideImage: Icon;

// Import aliases
export declare const ImportIcon: Icon;
export declare const LucideImport: Icon;

// Inbox aliases
export declare const InboxIcon: Icon;
export declare const LucideInbox: Icon;

// Indent aliases
export declare const IndentIcon: Icon;
export declare const LucideIndent: Icon;

// IndianRupee aliases
export declare const IndianRupeeIcon: Icon;
export declare const LucideIndianRupee: Icon;

// Infinity aliases
export declare const InfinityIcon: Icon;
export declare const LucideInfinity: Icon;

// Info aliases
export declare const InfoIcon: Icon;
export declare const LucideInfo: Icon;

// Instagram aliases
export declare const InstagramIcon: Icon;
export declare const LucideInstagram: Icon;

// Italic aliases
export declare const ItalicIcon: Icon;
export declare const LucideItalic: Icon;

// IterationCcw aliases
export declare const IterationCcwIcon: Icon;
export declare const LucideIterationCcw: Icon;

// IterationCw aliases
export declare const IterationCwIcon: Icon;
export declare const LucideIterationCw: Icon;

// JapaneseYen aliases
export declare const JapaneseYenIcon: Icon;
export declare const LucideJapaneseYen: Icon;

// Joystick aliases
export declare const JoystickIcon: Icon;
export declare const LucideJoystick: Icon;

// KanbanSquareDashed aliases
export declare const KanbanSquareDashedIcon: Icon;
export declare const LucideKanbanSquareDashed: Icon;
export declare const SquareKanbanDashed: Icon;

// KanbanSquare aliases
export declare const KanbanSquareIcon: Icon;
export declare const LucideKanbanSquare: Icon;
export declare const SquareKanban: Icon;

// Kanban aliases
export declare const KanbanIcon: Icon;
export declare const LucideKanban: Icon;

// KeyRound aliases
export declare const KeyRoundIcon: Icon;
export declare const LucideKeyRound: Icon;

// KeySquare aliases
export declare const KeySquareIcon: Icon;
export declare const LucideKeySquare: Icon;

// Key aliases
export declare const KeyIcon: Icon;
export declare const LucideKey: Icon;

// Keyboard aliases
export declare const KeyboardIcon: Icon;
export declare const LucideKeyboard: Icon;

// LampCeiling aliases
export declare const LampCeilingIcon: Icon;
export declare const LucideLampCeiling: Icon;

// LampDesk aliases
export declare const LampDeskIcon: Icon;
export declare const LucideLampDesk: Icon;

// LampFloor aliases
export declare const LampFloorIcon: Icon;
export declare const LucideLampFloor: Icon;

// LampWallDown aliases
export declare const LampWallDownIcon: Icon;
export declare const LucideLampWallDown: Icon;

// LampWallUp aliases
export declare const LampWallUpIcon: Icon;
export declare const LucideLampWallUp: Icon;

// Lamp aliases
export declare const LampIcon: Icon;
export declare const LucideLamp: Icon;

// LandPlot aliases
export declare const LandPlotIcon: Icon;
export declare const LucideLandPlot: Icon;

// Landmark aliases
export declare const LandmarkIcon: Icon;
export declare const LucideLandmark: Icon;

// Languages aliases
export declare const LanguagesIcon: Icon;
export declare const LucideLanguages: Icon;

// Laptop2 aliases
export declare const Laptop2Icon: Icon;
export declare const LucideLaptop2: Icon;

// Laptop aliases
export declare const LaptopIcon: Icon;
export declare const LucideLaptop: Icon;

// LassoSelect aliases
export declare const LassoSelectIcon: Icon;
export declare const LucideLassoSelect: Icon;

// Lasso aliases
export declare const LassoIcon: Icon;
export declare const LucideLasso: Icon;

// Laugh aliases
export declare const LaughIcon: Icon;
export declare const LucideLaugh: Icon;

// Layers2 aliases
export declare const Layers2Icon: Icon;
export declare const LucideLayers2: Icon;

// Layers3 aliases
export declare const Layers3Icon: Icon;
export declare const LucideLayers3: Icon;

// Layers aliases
export declare const LayersIcon: Icon;
export declare const LucideLayers: Icon;

// LayoutDashboard aliases
export declare const LayoutDashboardIcon: Icon;
export declare const LucideLayoutDashboard: Icon;

// LayoutGrid aliases
export declare const LayoutGridIcon: Icon;
export declare const LucideLayoutGrid: Icon;

// LayoutList aliases
export declare const LayoutListIcon: Icon;
export declare const LucideLayoutList: Icon;

// LayoutPanelLeft aliases
export declare const LayoutPanelLeftIcon: Icon;
export declare const LucideLayoutPanelLeft: Icon;

// LayoutPanelTop aliases
export declare const LayoutPanelTopIcon: Icon;
export declare const LucideLayoutPanelTop: Icon;

// LayoutTemplate aliases
export declare const LayoutTemplateIcon: Icon;
export declare const LucideLayoutTemplate: Icon;

// Layout aliases
export declare const LayoutIcon: Icon;
export declare const LucideLayout: Icon;

// Leaf aliases
export declare const LeafIcon: Icon;
export declare const LucideLeaf: Icon;

// LeafyGreen aliases
export declare const LeafyGreenIcon: Icon;
export declare const LucideLeafyGreen: Icon;

// LibraryBig aliases
export declare const LibraryBigIcon: Icon;
export declare const LucideLibraryBig: Icon;

// LibrarySquare aliases
export declare const LibrarySquareIcon: Icon;
export declare const LucideLibrarySquare: Icon;

// Library aliases
export declare const LibraryIcon: Icon;
export declare const LucideLibrary: Icon;

// LifeBuoy aliases
export declare const LifeBuoyIcon: Icon;
export declare const LucideLifeBuoy: Icon;

// Ligature aliases
export declare const LigatureIcon: Icon;
export declare const LucideLigature: Icon;

// LightbulbOff aliases
export declare const LightbulbOffIcon: Icon;
export declare const LucideLightbulbOff: Icon;

// Lightbulb aliases
export declare const LightbulbIcon: Icon;
export declare const LucideLightbulb: Icon;

// LineChart aliases
export declare const LineChartIcon: Icon;
export declare const LucideLineChart: Icon;

// Link2Off aliases
export declare const Link2OffIcon: Icon;
export declare const LucideLink2Off: Icon;

// Link2 aliases
export declare const Link2Icon: Icon;
export declare const LucideLink2: Icon;

// Link aliases
export declare const LinkIcon: Icon;
export declare const LucideLink: Icon;

// Linkedin aliases
export declare const LinkedinIcon: Icon;
export declare const LucideLinkedin: Icon;

// ListChecks aliases
export declare const ListChecksIcon: Icon;
export declare const LucideListChecks: Icon;

// ListEnd aliases
export declare const ListEndIcon: Icon;
export declare const LucideListEnd: Icon;

// ListFilter aliases
export declare const ListFilterIcon: Icon;
export declare const LucideListFilter: Icon;

// ListMinus aliases
export declare const ListMinusIcon: Icon;
export declare const LucideListMinus: Icon;

// ListMusic aliases
export declare const ListMusicIcon: Icon;
export declare const LucideListMusic: Icon;

// ListOrdered aliases
export declare const ListOrderedIcon: Icon;
export declare const LucideListOrdered: Icon;

// ListPlus aliases
export declare const ListPlusIcon: Icon;
export declare const LucideListPlus: Icon;

// ListRestart aliases
export declare const ListRestartIcon: Icon;
export declare const LucideListRestart: Icon;

// ListStart aliases
export declare const ListStartIcon: Icon;
export declare const LucideListStart: Icon;

// ListTodo aliases
export declare const ListTodoIcon: Icon;
export declare const LucideListTodo: Icon;

// ListTree aliases
export declare const ListTreeIcon: Icon;
export declare const LucideListTree: Icon;

// ListVideo aliases
export declare const ListVideoIcon: Icon;
export declare const LucideListVideo: Icon;

// ListX aliases
export declare const ListXIcon: Icon;
export declare const LucideListX: Icon;

// List aliases
export declare const ListIcon: Icon;
export declare const LucideList: Icon;

// Loader2 aliases
export declare const Loader2Icon: Icon;
export declare const LucideLoader2: Icon;

// Loader aliases
export declare const LoaderIcon: Icon;
export declare const LucideLoader: Icon;

// LocateFixed aliases
export declare const LocateFixedIcon: Icon;
export declare const LucideLocateFixed: Icon;

// LocateOff aliases
export declare const LocateOffIcon: Icon;
export declare const LucideLocateOff: Icon;

// Locate aliases
export declare const LocateIcon: Icon;
export declare const LucideLocate: Icon;

// LockKeyhole aliases
export declare const LockKeyholeIcon: Icon;
export declare const LucideLockKeyhole: Icon;

// Lock aliases
export declare const LockIcon: Icon;
export declare const LucideLock: Icon;

// LogIn aliases
export declare const LogInIcon: Icon;
export declare const LucideLogIn: Icon;

// LogOut aliases
export declare const LogOutIcon: Icon;
export declare const LucideLogOut: Icon;

// Lollipop aliases
export declare const LollipopIcon: Icon;
export declare const LucideLollipop: Icon;

// Luggage aliases
export declare const LuggageIcon: Icon;
export declare const LucideLuggage: Icon;

// MSquare aliases
export declare const MSquareIcon: Icon;
export declare const LucideMSquare: Icon;

// Magnet aliases
export declare const MagnetIcon: Icon;
export declare const LucideMagnet: Icon;

// MailCheck aliases
export declare const MailCheckIcon: Icon;
export declare const LucideMailCheck: Icon;

// MailMinus aliases
export declare const MailMinusIcon: Icon;
export declare const LucideMailMinus: Icon;

// MailOpen aliases
export declare const MailOpenIcon: Icon;
export declare const LucideMailOpen: Icon;

// MailPlus aliases
export declare const MailPlusIcon: Icon;
export declare const LucideMailPlus: Icon;

// MailQuestion aliases
export declare const MailQuestionIcon: Icon;
export declare const LucideMailQuestion: Icon;

// MailSearch aliases
export declare const MailSearchIcon: Icon;
export declare const LucideMailSearch: Icon;

// MailWarning aliases
export declare const MailWarningIcon: Icon;
export declare const LucideMailWarning: Icon;

// MailX aliases
export declare const MailXIcon: Icon;
export declare const LucideMailX: Icon;

// Mail aliases
export declare const MailIcon: Icon;
export declare const LucideMail: Icon;

// Mailbox aliases
export declare const MailboxIcon: Icon;
export declare const LucideMailbox: Icon;

// Mails aliases
export declare const MailsIcon: Icon;
export declare const LucideMails: Icon;

// MapPinOff aliases
export declare const MapPinOffIcon: Icon;
export declare const LucideMapPinOff: Icon;

// MapPin aliases
export declare const MapPinIcon: Icon;
export declare const LucideMapPin: Icon;

// MapPinned aliases
export declare const MapPinnedIcon: Icon;
export declare const LucideMapPinned: Icon;

// Map aliases
export declare const MapIcon: Icon;
export declare const LucideMap: Icon;

// Martini aliases
export declare const MartiniIcon: Icon;
export declare const LucideMartini: Icon;

// Maximize2 aliases
export declare const Maximize2Icon: Icon;
export declare const LucideMaximize2: Icon;

// Maximize aliases
export declare const MaximizeIcon: Icon;
export declare const LucideMaximize: Icon;

// Medal aliases
export declare const MedalIcon: Icon;
export declare const LucideMedal: Icon;

// MegaphoneOff aliases
export declare const MegaphoneOffIcon: Icon;
export declare const LucideMegaphoneOff: Icon;

// Megaphone aliases
export declare const MegaphoneIcon: Icon;
export declare const LucideMegaphone: Icon;

// Meh aliases
export declare const MehIcon: Icon;
export declare const LucideMeh: Icon;

// MemoryStick aliases
export declare const MemoryStickIcon: Icon;
export declare const LucideMemoryStick: Icon;

// MenuSquare aliases
export declare const MenuSquareIcon: Icon;
export declare const LucideMenuSquare: Icon;

// Menu aliases
export declare const MenuIcon: Icon;
export declare const LucideMenu: Icon;

// Merge aliases
export declare const MergeIcon: Icon;
export declare const LucideMerge: Icon;

// MessageCircle aliases
export declare const MessageCircleIcon: Icon;
export declare const LucideMessageCircle: Icon;

// MessageSquareDashed aliases
export declare const MessageSquareDashedIcon: Icon;
export declare const LucideMessageSquareDashed: Icon;

// MessageSquarePlus aliases
export declare const MessageSquarePlusIcon: Icon;
export declare const LucideMessageSquarePlus: Icon;

// MessageSquare aliases
export declare const MessageSquareIcon: Icon;
export declare const LucideMessageSquare: Icon;

// MessagesSquare aliases
export declare const MessagesSquareIcon: Icon;
export declare const LucideMessagesSquare: Icon;

// Mic2 aliases
export declare const Mic2Icon: Icon;
export declare const LucideMic2: Icon;

// MicOff aliases
export declare const MicOffIcon: Icon;
export declare const LucideMicOff: Icon;

// Mic aliases
export declare const MicIcon: Icon;
export declare const LucideMic: Icon;

// Microscope aliases
export declare const MicroscopeIcon: Icon;
export declare const LucideMicroscope: Icon;

// Microwave aliases
export declare const MicrowaveIcon: Icon;
export declare const LucideMicrowave: Icon;

// Milestone aliases
export declare const MilestoneIcon: Icon;
export declare const LucideMilestone: Icon;

// MilkOff aliases
export declare const MilkOffIcon: Icon;
export declare const LucideMilkOff: Icon;

// Milk aliases
export declare const MilkIcon: Icon;
export declare const LucideMilk: Icon;

// Minimize2 aliases
export declare const Minimize2Icon: Icon;
export declare const LucideMinimize2: Icon;

// Minimize aliases
export declare const MinimizeIcon: Icon;
export declare const LucideMinimize: Icon;

// MinusCircle aliases
export declare const MinusCircleIcon: Icon;
export declare const LucideMinusCircle: Icon;

// MinusSquare aliases
export declare const MinusSquareIcon: Icon;
export declare const LucideMinusSquare: Icon;

// Minus aliases
export declare const MinusIcon: Icon;
export declare const LucideMinus: Icon;

// MonitorCheck aliases
export declare const MonitorCheckIcon: Icon;
export declare const LucideMonitorCheck: Icon;

// MonitorDot aliases
export declare const MonitorDotIcon: Icon;
export declare const LucideMonitorDot: Icon;

// MonitorDown aliases
export declare const MonitorDownIcon: Icon;
export declare const LucideMonitorDown: Icon;

// MonitorOff aliases
export declare const MonitorOffIcon: Icon;
export declare const LucideMonitorOff: Icon;

// MonitorPause aliases
export declare const MonitorPauseIcon: Icon;
export declare const LucideMonitorPause: Icon;

// MonitorPlay aliases
export declare const MonitorPlayIcon: Icon;
export declare const LucideMonitorPlay: Icon;

// MonitorSmartphone aliases
export declare const MonitorSmartphoneIcon: Icon;
export declare const LucideMonitorSmartphone: Icon;

// MonitorSpeaker aliases
export declare const MonitorSpeakerIcon: Icon;
export declare const LucideMonitorSpeaker: Icon;

// MonitorStop aliases
export declare const MonitorStopIcon: Icon;
export declare const LucideMonitorStop: Icon;

// MonitorUp aliases
export declare const MonitorUpIcon: Icon;
export declare const LucideMonitorUp: Icon;

// MonitorX aliases
export declare const MonitorXIcon: Icon;
export declare const LucideMonitorX: Icon;

// Monitor aliases
export declare const MonitorIcon: Icon;
export declare const LucideMonitor: Icon;

// MoonStar aliases
export declare const MoonStarIcon: Icon;
export declare const LucideMoonStar: Icon;

// Moon aliases
export declare const MoonIcon: Icon;
export declare const LucideMoon: Icon;

// MoreHorizontal aliases
export declare const MoreHorizontalIcon: Icon;
export declare const LucideMoreHorizontal: Icon;

// MoreVertical aliases
export declare const MoreVerticalIcon: Icon;
export declare const LucideMoreVertical: Icon;

// MountainSnow aliases
export declare const MountainSnowIcon: Icon;
export declare const LucideMountainSnow: Icon;

// Mountain aliases
export declare const MountainIcon: Icon;
export declare const LucideMountain: Icon;

// MousePointer2 aliases
export declare const MousePointer2Icon: Icon;
export declare const LucideMousePointer2: Icon;

// MousePointerClick aliases
export declare const MousePointerClickIcon: Icon;
export declare const LucideMousePointerClick: Icon;

// MousePointerSquareDashed aliases
export declare const MousePointerSquareDashedIcon: Icon;
export declare const LucideMousePointerSquareDashed: Icon;

// MousePointerSquare aliases
export declare const MousePointerSquareIcon: Icon;
export declare const LucideMousePointerSquare: Icon;
export declare const Inspect: Icon;

// MousePointer aliases
export declare const MousePointerIcon: Icon;
export declare const LucideMousePointer: Icon;

// Mouse aliases
export declare const MouseIcon: Icon;
export declare const LucideMouse: Icon;

// Move3d aliases
export declare const Move3dIcon: Icon;
export declare const LucideMove3d: Icon;
export declare const Move3D: Icon;

// MoveDiagonal2 aliases
export declare const MoveDiagonal2Icon: Icon;
export declare const LucideMoveDiagonal2: Icon;

// MoveDiagonal aliases
export declare const MoveDiagonalIcon: Icon;
export declare const LucideMoveDiagonal: Icon;

// MoveDownLeft aliases
export declare const MoveDownLeftIcon: Icon;
export declare const LucideMoveDownLeft: Icon;

// MoveDownRight aliases
export declare const MoveDownRightIcon: Icon;
export declare const LucideMoveDownRight: Icon;

// MoveDown aliases
export declare const MoveDownIcon: Icon;
export declare const LucideMoveDown: Icon;

// MoveHorizontal aliases
export declare const MoveHorizontalIcon: Icon;
export declare const LucideMoveHorizontal: Icon;

// MoveLeft aliases
export declare const MoveLeftIcon: Icon;
export declare const LucideMoveLeft: Icon;

// MoveRight aliases
export declare const MoveRightIcon: Icon;
export declare const LucideMoveRight: Icon;

// MoveUpLeft aliases
export declare const MoveUpLeftIcon: Icon;
export declare const LucideMoveUpLeft: Icon;

// MoveUpRight aliases
export declare const MoveUpRightIcon: Icon;
export declare const LucideMoveUpRight: Icon;

// MoveUp aliases
export declare const MoveUpIcon: Icon;
export declare const LucideMoveUp: Icon;

// MoveVertical aliases
export declare const MoveVerticalIcon: Icon;
export declare const LucideMoveVertical: Icon;

// Move aliases
export declare const MoveIcon: Icon;
export declare const LucideMove: Icon;

// Music2 aliases
export declare const Music2Icon: Icon;
export declare const LucideMusic2: Icon;

// Music3 aliases
export declare const Music3Icon: Icon;
export declare const LucideMusic3: Icon;

// Music4 aliases
export declare const Music4Icon: Icon;
export declare const LucideMusic4: Icon;

// Music aliases
export declare const MusicIcon: Icon;
export declare const LucideMusic: Icon;

// Navigation2Off aliases
export declare const Navigation2OffIcon: Icon;
export declare const LucideNavigation2Off: Icon;

// Navigation2 aliases
export declare const Navigation2Icon: Icon;
export declare const LucideNavigation2: Icon;

// NavigationOff aliases
export declare const NavigationOffIcon: Icon;
export declare const LucideNavigationOff: Icon;

// Navigation aliases
export declare const NavigationIcon: Icon;
export declare const LucideNavigation: Icon;

// Network aliases
export declare const NetworkIcon: Icon;
export declare const LucideNetwork: Icon;

// Newspaper aliases
export declare const NewspaperIcon: Icon;
export declare const LucideNewspaper: Icon;

// Nfc aliases
export declare const NfcIcon: Icon;
export declare const LucideNfc: Icon;

// NutOff aliases
export declare const NutOffIcon: Icon;
export declare const LucideNutOff: Icon;

// Nut aliases
export declare const NutIcon: Icon;
export declare const LucideNut: Icon;

// Octagon aliases
export declare const OctagonIcon: Icon;
export declare const LucideOctagon: Icon;

// Option aliases
export declare const OptionIcon: Icon;
export declare const LucideOption: Icon;

// Orbit aliases
export declare const OrbitIcon: Icon;
export declare const LucideOrbit: Icon;

// Outdent aliases
export declare const OutdentIcon: Icon;
export declare const LucideOutdent: Icon;

// Package2 aliases
export declare const Package2Icon: Icon;
export declare const LucidePackage2: Icon;

// PackageCheck aliases
export declare const PackageCheckIcon: Icon;
export declare const LucidePackageCheck: Icon;

// PackageMinus aliases
export declare const PackageMinusIcon: Icon;
export declare const LucidePackageMinus: Icon;

// PackageOpen aliases
export declare const PackageOpenIcon: Icon;
export declare const LucidePackageOpen: Icon;

// PackagePlus aliases
export declare const PackagePlusIcon: Icon;
export declare const LucidePackagePlus: Icon;

// PackageSearch aliases
export declare const PackageSearchIcon: Icon;
export declare const LucidePackageSearch: Icon;

// PackageX aliases
export declare const PackageXIcon: Icon;
export declare const LucidePackageX: Icon;

// Package aliases
export declare const PackageIcon: Icon;
export declare const LucidePackage: Icon;

// PaintBucket aliases
export declare const PaintBucketIcon: Icon;
export declare const LucidePaintBucket: Icon;

// Paintbrush2 aliases
export declare const Paintbrush2Icon: Icon;
export declare const LucidePaintbrush2: Icon;

// Paintbrush aliases
export declare const PaintbrushIcon: Icon;
export declare const LucidePaintbrush: Icon;

// Palette aliases
export declare const PaletteIcon: Icon;
export declare const LucidePalette: Icon;

// Palmtree aliases
export declare const PalmtreeIcon: Icon;
export declare const LucidePalmtree: Icon;

// PanelBottomClose aliases
export declare const PanelBottomCloseIcon: Icon;
export declare const LucidePanelBottomClose: Icon;

// PanelBottomInactive aliases
export declare const PanelBottomInactiveIcon: Icon;
export declare const LucidePanelBottomInactive: Icon;

// PanelBottomOpen aliases
export declare const PanelBottomOpenIcon: Icon;
export declare const LucidePanelBottomOpen: Icon;

// PanelBottom aliases
export declare const PanelBottomIcon: Icon;
export declare const LucidePanelBottom: Icon;

// PanelLeftClose aliases
export declare const PanelLeftCloseIcon: Icon;
export declare const LucidePanelLeftClose: Icon;
export declare const SidebarClose: Icon;

// PanelLeftInactive aliases
export declare const PanelLeftInactiveIcon: Icon;
export declare const LucidePanelLeftInactive: Icon;

// PanelLeftOpen aliases
export declare const PanelLeftOpenIcon: Icon;
export declare const LucidePanelLeftOpen: Icon;
export declare const SidebarOpen: Icon;

// PanelLeft aliases
export declare const PanelLeftIcon: Icon;
export declare const LucidePanelLeft: Icon;
export declare const Sidebar: Icon;

// PanelRightClose aliases
export declare const PanelRightCloseIcon: Icon;
export declare const LucidePanelRightClose: Icon;

// PanelRightInactive aliases
export declare const PanelRightInactiveIcon: Icon;
export declare const LucidePanelRightInactive: Icon;

// PanelRightOpen aliases
export declare const PanelRightOpenIcon: Icon;
export declare const LucidePanelRightOpen: Icon;

// PanelRight aliases
export declare const PanelRightIcon: Icon;
export declare const LucidePanelRight: Icon;

// PanelTopClose aliases
export declare const PanelTopCloseIcon: Icon;
export declare const LucidePanelTopClose: Icon;

// PanelTopInactive aliases
export declare const PanelTopInactiveIcon: Icon;
export declare const LucidePanelTopInactive: Icon;

// PanelTopOpen aliases
export declare const PanelTopOpenIcon: Icon;
export declare const LucidePanelTopOpen: Icon;

// PanelTop aliases
export declare const PanelTopIcon: Icon;
export declare const LucidePanelTop: Icon;

// Paperclip aliases
export declare const PaperclipIcon: Icon;
export declare const LucidePaperclip: Icon;

// Parentheses aliases
export declare const ParenthesesIcon: Icon;
export declare const LucideParentheses: Icon;

// ParkingCircleOff aliases
export declare const ParkingCircleOffIcon: Icon;
export declare const LucideParkingCircleOff: Icon;

// ParkingCircle aliases
export declare const ParkingCircleIcon: Icon;
export declare const LucideParkingCircle: Icon;

// ParkingMeter aliases
export declare const ParkingMeterIcon: Icon;
export declare const LucideParkingMeter: Icon;

// ParkingSquareOff aliases
export declare const ParkingSquareOffIcon: Icon;
export declare const LucideParkingSquareOff: Icon;

// ParkingSquare aliases
export declare const ParkingSquareIcon: Icon;
export declare const LucideParkingSquare: Icon;

// PartyPopper aliases
export declare const PartyPopperIcon: Icon;
export declare const LucidePartyPopper: Icon;

// PauseCircle aliases
export declare const PauseCircleIcon: Icon;
export declare const LucidePauseCircle: Icon;

// PauseOctagon aliases
export declare const PauseOctagonIcon: Icon;
export declare const LucidePauseOctagon: Icon;

// Pause aliases
export declare const PauseIcon: Icon;
export declare const LucidePause: Icon;

// PawPrint aliases
export declare const PawPrintIcon: Icon;
export declare const LucidePawPrint: Icon;

// PcCase aliases
export declare const PcCaseIcon: Icon;
export declare const LucidePcCase: Icon;

// PenLine aliases
export declare const PenLineIcon: Icon;
export declare const LucidePenLine: Icon;
export declare const Edit3: Icon;

// PenSquare aliases
export declare const PenSquareIcon: Icon;
export declare const LucidePenSquare: Icon;
export declare const PenBox: Icon;
export declare const Edit: Icon;

// PenTool aliases
export declare const PenToolIcon: Icon;
export declare const LucidePenTool: Icon;

// Pen aliases
export declare const PenIcon: Icon;
export declare const LucidePen: Icon;
export declare const Edit2: Icon;

// PencilLine aliases
export declare const PencilLineIcon: Icon;
export declare const LucidePencilLine: Icon;

// PencilRuler aliases
export declare const PencilRulerIcon: Icon;
export declare const LucidePencilRuler: Icon;

// Pencil aliases
export declare const PencilIcon: Icon;
export declare const LucidePencil: Icon;

// Pentagon aliases
export declare const PentagonIcon: Icon;
export declare const LucidePentagon: Icon;

// PercentCircle aliases
export declare const PercentCircleIcon: Icon;
export declare const LucidePercentCircle: Icon;

// PercentDiamond aliases
export declare const PercentDiamondIcon: Icon;
export declare const LucidePercentDiamond: Icon;

// PercentSquare aliases
export declare const PercentSquareIcon: Icon;
export declare const LucidePercentSquare: Icon;

// Percent aliases
export declare const PercentIcon: Icon;
export declare const LucidePercent: Icon;

// PersonStanding aliases
export declare const PersonStandingIcon: Icon;
export declare const LucidePersonStanding: Icon;

// PhoneCall aliases
export declare const PhoneCallIcon: Icon;
export declare const LucidePhoneCall: Icon;

// PhoneForwarded aliases
export declare const PhoneForwardedIcon: Icon;
export declare const LucidePhoneForwarded: Icon;

// PhoneIncoming aliases
export declare const PhoneIncomingIcon: Icon;
export declare const LucidePhoneIncoming: Icon;

// PhoneMissed aliases
export declare const PhoneMissedIcon: Icon;
export declare const LucidePhoneMissed: Icon;

// PhoneOff aliases
export declare const PhoneOffIcon: Icon;
export declare const LucidePhoneOff: Icon;

// PhoneOutgoing aliases
export declare const PhoneOutgoingIcon: Icon;
export declare const LucidePhoneOutgoing: Icon;

// Phone aliases
export declare const PhoneIcon: Icon;
export declare const LucidePhone: Icon;

// PiSquare aliases
export declare const PiSquareIcon: Icon;
export declare const LucidePiSquare: Icon;

// Pi aliases
export declare const PiIcon: Icon;
export declare const LucidePi: Icon;

// PictureInPicture2 aliases
export declare const PictureInPicture2Icon: Icon;
export declare const LucidePictureInPicture2: Icon;

// PictureInPicture aliases
export declare const PictureInPictureIcon: Icon;
export declare const LucidePictureInPicture: Icon;

// PieChart aliases
export declare const PieChartIcon: Icon;
export declare const LucidePieChart: Icon;

// PiggyBank aliases
export declare const PiggyBankIcon: Icon;
export declare const LucidePiggyBank: Icon;

// PilcrowSquare aliases
export declare const PilcrowSquareIcon: Icon;
export declare const LucidePilcrowSquare: Icon;

// Pilcrow aliases
export declare const PilcrowIcon: Icon;
export declare const LucidePilcrow: Icon;

// Pill aliases
export declare const PillIcon: Icon;
export declare const LucidePill: Icon;

// PinOff aliases
export declare const PinOffIcon: Icon;
export declare const LucidePinOff: Icon;

// Pin aliases
export declare const PinIcon: Icon;
export declare const LucidePin: Icon;

// Pipette aliases
export declare const PipetteIcon: Icon;
export declare const LucidePipette: Icon;

// Pizza aliases
export declare const PizzaIcon: Icon;
export declare const LucidePizza: Icon;

// PlaneLanding aliases
export declare const PlaneLandingIcon: Icon;
export declare const LucidePlaneLanding: Icon;

// PlaneTakeoff aliases
export declare const PlaneTakeoffIcon: Icon;
export declare const LucidePlaneTakeoff: Icon;

// Plane aliases
export declare const PlaneIcon: Icon;
export declare const LucidePlane: Icon;

// PlayCircle aliases
export declare const PlayCircleIcon: Icon;
export declare const LucidePlayCircle: Icon;

// PlaySquare aliases
export declare const PlaySquareIcon: Icon;
export declare const LucidePlaySquare: Icon;

// Play aliases
export declare const PlayIcon: Icon;
export declare const LucidePlay: Icon;

// Plug2 aliases
export declare const Plug2Icon: Icon;
export declare const LucidePlug2: Icon;

// PlugZap2 aliases
export declare const PlugZap2Icon: Icon;
export declare const LucidePlugZap2: Icon;

// PlugZap aliases
export declare const PlugZapIcon: Icon;
export declare const LucidePlugZap: Icon;

// Plug aliases
export declare const PlugIcon: Icon;
export declare const LucidePlug: Icon;

// PlusCircle aliases
export declare const PlusCircleIcon: Icon;
export declare const LucidePlusCircle: Icon;

// PlusSquare aliases
export declare const PlusSquareIcon: Icon;
export declare const LucidePlusSquare: Icon;

// Plus aliases
export declare const PlusIcon: Icon;
export declare const LucidePlus: Icon;

// PocketKnife aliases
export declare const PocketKnifeIcon: Icon;
export declare const LucidePocketKnife: Icon;

// Pocket aliases
export declare const PocketIcon: Icon;
export declare const LucidePocket: Icon;

// Podcast aliases
export declare const PodcastIcon: Icon;
export declare const LucidePodcast: Icon;

// Pointer aliases
export declare const PointerIcon: Icon;
export declare const LucidePointer: Icon;

// Popcorn aliases
export declare const PopcornIcon: Icon;
export declare const LucidePopcorn: Icon;

// Popsicle aliases
export declare const PopsicleIcon: Icon;
export declare const LucidePopsicle: Icon;

// PoundSterling aliases
export declare const PoundSterlingIcon: Icon;
export declare const LucidePoundSterling: Icon;

// PowerCircle aliases
export declare const PowerCircleIcon: Icon;
export declare const LucidePowerCircle: Icon;

// PowerOff aliases
export declare const PowerOffIcon: Icon;
export declare const LucidePowerOff: Icon;

// PowerSquare aliases
export declare const PowerSquareIcon: Icon;
export declare const LucidePowerSquare: Icon;

// Power aliases
export declare const PowerIcon: Icon;
export declare const LucidePower: Icon;

// Presentation aliases
export declare const PresentationIcon: Icon;
export declare const LucidePresentation: Icon;

// Printer aliases
export declare const PrinterIcon: Icon;
export declare const LucidePrinter: Icon;

// Projector aliases
export declare const ProjectorIcon: Icon;
export declare const LucideProjector: Icon;

// Puzzle aliases
export declare const PuzzleIcon: Icon;
export declare const LucidePuzzle: Icon;

// Pyramid aliases
export declare const PyramidIcon: Icon;
export declare const LucidePyramid: Icon;

// QrCode aliases
export declare const QrCodeIcon: Icon;
export declare const LucideQrCode: Icon;

// Quote aliases
export declare const QuoteIcon: Icon;
export declare const LucideQuote: Icon;

// Rabbit aliases
export declare const RabbitIcon: Icon;
export declare const LucideRabbit: Icon;

// Radar aliases
export declare const RadarIcon: Icon;
export declare const LucideRadar: Icon;

// Radiation aliases
export declare const RadiationIcon: Icon;
export declare const LucideRadiation: Icon;

// RadioReceiver aliases
export declare const RadioReceiverIcon: Icon;
export declare const LucideRadioReceiver: Icon;

// RadioTower aliases
export declare const RadioTowerIcon: Icon;
export declare const LucideRadioTower: Icon;

// Radio aliases
export declare const RadioIcon: Icon;
export declare const LucideRadio: Icon;

// Radius aliases
export declare const RadiusIcon: Icon;
export declare const LucideRadius: Icon;

// RailSymbol aliases
export declare const RailSymbolIcon: Icon;
export declare const LucideRailSymbol: Icon;

// Rainbow aliases
export declare const RainbowIcon: Icon;
export declare const LucideRainbow: Icon;

// Rat aliases
export declare const RatIcon: Icon;
export declare const LucideRat: Icon;

// Ratio aliases
export declare const RatioIcon: Icon;
export declare const LucideRatio: Icon;

// Receipt aliases
export declare const ReceiptIcon: Icon;
export declare const LucideReceipt: Icon;

// RectangleHorizontal aliases
export declare const RectangleHorizontalIcon: Icon;
export declare const LucideRectangleHorizontal: Icon;

// RectangleVertical aliases
export declare const RectangleVerticalIcon: Icon;
export declare const LucideRectangleVertical: Icon;

// Recycle aliases
export declare const RecycleIcon: Icon;
export declare const LucideRecycle: Icon;

// Redo2 aliases
export declare const Redo2Icon: Icon;
export declare const LucideRedo2: Icon;

// RedoDot aliases
export declare const RedoDotIcon: Icon;
export declare const LucideRedoDot: Icon;

// Redo aliases
export declare const RedoIcon: Icon;
export declare const LucideRedo: Icon;

// RefreshCcwDot aliases
export declare const RefreshCcwDotIcon: Icon;
export declare const LucideRefreshCcwDot: Icon;

// RefreshCcw aliases
export declare const RefreshCcwIcon: Icon;
export declare const LucideRefreshCcw: Icon;

// RefreshCwOff aliases
export declare const RefreshCwOffIcon: Icon;
export declare const LucideRefreshCwOff: Icon;

// RefreshCw aliases
export declare const RefreshCwIcon: Icon;
export declare const LucideRefreshCw: Icon;

// Refrigerator aliases
export declare const RefrigeratorIcon: Icon;
export declare const LucideRefrigerator: Icon;

// Regex aliases
export declare const RegexIcon: Icon;
export declare const LucideRegex: Icon;

// RemoveFormatting aliases
export declare const RemoveFormattingIcon: Icon;
export declare const LucideRemoveFormatting: Icon;

// Repeat1 aliases
export declare const Repeat1Icon: Icon;
export declare const LucideRepeat1: Icon;

// Repeat2 aliases
export declare const Repeat2Icon: Icon;
export declare const LucideRepeat2: Icon;

// Repeat aliases
export declare const RepeatIcon: Icon;
export declare const LucideRepeat: Icon;

// ReplaceAll aliases
export declare const ReplaceAllIcon: Icon;
export declare const LucideReplaceAll: Icon;

// Replace aliases
export declare const ReplaceIcon: Icon;
export declare const LucideReplace: Icon;

// ReplyAll aliases
export declare const ReplyAllIcon: Icon;
export declare const LucideReplyAll: Icon;

// Reply aliases
export declare const ReplyIcon: Icon;
export declare const LucideReply: Icon;

// Rewind aliases
export declare const RewindIcon: Icon;
export declare const LucideRewind: Icon;

// Ribbon aliases
export declare const RibbonIcon: Icon;
export declare const LucideRibbon: Icon;

// Rocket aliases
export declare const RocketIcon: Icon;
export declare const LucideRocket: Icon;

// RockingChair aliases
export declare const RockingChairIcon: Icon;
export declare const LucideRockingChair: Icon;

// RollerCoaster aliases
export declare const RollerCoasterIcon: Icon;
export declare const LucideRollerCoaster: Icon;

// Rotate3d aliases
export declare const Rotate3dIcon: Icon;
export declare const LucideRotate3d: Icon;
export declare const Rotate3D: Icon;

// RotateCcw aliases
export declare const RotateCcwIcon: Icon;
export declare const LucideRotateCcw: Icon;

// RotateCw aliases
export declare const RotateCwIcon: Icon;
export declare const LucideRotateCw: Icon;

// RouteOff aliases
export declare const RouteOffIcon: Icon;
export declare const LucideRouteOff: Icon;

// Route aliases
export declare const RouteIcon: Icon;
export declare const LucideRoute: Icon;

// Router aliases
export declare const RouterIcon: Icon;
export declare const LucideRouter: Icon;

// Rows aliases
export declare const RowsIcon: Icon;
export declare const LucideRows: Icon;

// Rss aliases
export declare const RssIcon: Icon;
export declare const LucideRss: Icon;

// Ruler aliases
export declare const RulerIcon: Icon;
export declare const LucideRuler: Icon;

// RussianRuble aliases
export declare const RussianRubleIcon: Icon;
export declare const LucideRussianRuble: Icon;

// Sailboat aliases
export declare const SailboatIcon: Icon;
export declare const LucideSailboat: Icon;

// Salad aliases
export declare const SaladIcon: Icon;
export declare const LucideSalad: Icon;

// Sandwich aliases
export declare const SandwichIcon: Icon;
export declare const LucideSandwich: Icon;

// SatelliteDish aliases
export declare const SatelliteDishIcon: Icon;
export declare const LucideSatelliteDish: Icon;

// Satellite aliases
export declare const SatelliteIcon: Icon;
export declare const LucideSatellite: Icon;

// SaveAll aliases
export declare const SaveAllIcon: Icon;
export declare const LucideSaveAll: Icon;

// Save aliases
export declare const SaveIcon: Icon;
export declare const LucideSave: Icon;

// Scale3d aliases
export declare const Scale3dIcon: Icon;
export declare const LucideScale3d: Icon;
export declare const Scale3D: Icon;

// Scale aliases
export declare const ScaleIcon: Icon;
export declare const LucideScale: Icon;

// Scaling aliases
export declare const ScalingIcon: Icon;
export declare const LucideScaling: Icon;

// ScanBarcode aliases
export declare const ScanBarcodeIcon: Icon;
export declare const LucideScanBarcode: Icon;

// ScanEye aliases
export declare const ScanEyeIcon: Icon;
export declare const LucideScanEye: Icon;

// ScanFace aliases
export declare const ScanFaceIcon: Icon;
export declare const LucideScanFace: Icon;

// ScanLine aliases
export declare const ScanLineIcon: Icon;
export declare const LucideScanLine: Icon;

// ScanSearch aliases
export declare const ScanSearchIcon: Icon;
export declare const LucideScanSearch: Icon;

// ScanText aliases
export declare const ScanTextIcon: Icon;
export declare const LucideScanText: Icon;

// Scan aliases
export declare const ScanIcon: Icon;
export declare const LucideScan: Icon;

// ScatterChart aliases
export declare const ScatterChartIcon: Icon;
export declare const LucideScatterChart: Icon;

// School2 aliases
export declare const School2Icon: Icon;
export declare const LucideSchool2: Icon;

// School aliases
export declare const SchoolIcon: Icon;
export declare const LucideSchool: Icon;

// ScissorsLineDashed aliases
export declare const ScissorsLineDashedIcon: Icon;
export declare const LucideScissorsLineDashed: Icon;

// ScissorsSquareDashedBottom aliases
export declare const ScissorsSquareDashedBottomIcon: Icon;
export declare const LucideScissorsSquareDashedBottom: Icon;

// ScissorsSquare aliases
export declare const ScissorsSquareIcon: Icon;
export declare const LucideScissorsSquare: Icon;

// Scissors aliases
export declare const ScissorsIcon: Icon;
export declare const LucideScissors: Icon;

// ScreenShareOff aliases
export declare const ScreenShareOffIcon: Icon;
export declare const LucideScreenShareOff: Icon;

// ScreenShare aliases
export declare const ScreenShareIcon: Icon;
export declare const LucideScreenShare: Icon;

// ScrollText aliases
export declare const ScrollTextIcon: Icon;
export declare const LucideScrollText: Icon;

// Scroll aliases
export declare const ScrollIcon: Icon;
export declare const LucideScroll: Icon;

// SearchCheck aliases
export declare const SearchCheckIcon: Icon;
export declare const LucideSearchCheck: Icon;

// SearchCode aliases
export declare const SearchCodeIcon: Icon;
export declare const LucideSearchCode: Icon;

// SearchSlash aliases
export declare const SearchSlashIcon: Icon;
export declare const LucideSearchSlash: Icon;

// SearchX aliases
export declare const SearchXIcon: Icon;
export declare const LucideSearchX: Icon;

// Search aliases
export declare const SearchIcon: Icon;
export declare const LucideSearch: Icon;

// SendHorizontal aliases
export declare const SendHorizontalIcon: Icon;
export declare const LucideSendHorizontal: Icon;
export declare const SendHorizonal: Icon;

// SendToBack aliases
export declare const SendToBackIcon: Icon;
export declare const LucideSendToBack: Icon;

// Send aliases
export declare const SendIcon: Icon;
export declare const LucideSend: Icon;

// SeparatorHorizontal aliases
export declare const SeparatorHorizontalIcon: Icon;
export declare const LucideSeparatorHorizontal: Icon;

// SeparatorVertical aliases
export declare const SeparatorVerticalIcon: Icon;
export declare const LucideSeparatorVertical: Icon;

// ServerCog aliases
export declare const ServerCogIcon: Icon;
export declare const LucideServerCog: Icon;

// ServerCrash aliases
export declare const ServerCrashIcon: Icon;
export declare const LucideServerCrash: Icon;

// ServerOff aliases
export declare const ServerOffIcon: Icon;
export declare const LucideServerOff: Icon;

// Server aliases
export declare const ServerIcon: Icon;
export declare const LucideServer: Icon;

// Settings2 aliases
export declare const Settings2Icon: Icon;
export declare const LucideSettings2: Icon;

// Settings aliases
export declare const SettingsIcon: Icon;
export declare const LucideSettings: Icon;

// Shapes aliases
export declare const ShapesIcon: Icon;
export declare const LucideShapes: Icon;

// Share2 aliases
export declare const Share2Icon: Icon;
export declare const LucideShare2: Icon;

// Share aliases
export declare const ShareIcon: Icon;
export declare const LucideShare: Icon;

// Sheet aliases
export declare const SheetIcon: Icon;
export declare const LucideSheet: Icon;

// Shell aliases
export declare const ShellIcon: Icon;
export declare const LucideShell: Icon;

// ShieldAlert aliases
export declare const ShieldAlertIcon: Icon;
export declare const LucideShieldAlert: Icon;

// ShieldBan aliases
export declare const ShieldBanIcon: Icon;
export declare const LucideShieldBan: Icon;

// ShieldCheck aliases
export declare const ShieldCheckIcon: Icon;
export declare const LucideShieldCheck: Icon;

// ShieldEllipsis aliases
export declare const ShieldEllipsisIcon: Icon;
export declare const LucideShieldEllipsis: Icon;

// ShieldHalf aliases
export declare const ShieldHalfIcon: Icon;
export declare const LucideShieldHalf: Icon;

// ShieldMinus aliases
export declare const ShieldMinusIcon: Icon;
export declare const LucideShieldMinus: Icon;

// ShieldOff aliases
export declare const ShieldOffIcon: Icon;
export declare const LucideShieldOff: Icon;

// ShieldPlus aliases
export declare const ShieldPlusIcon: Icon;
export declare const LucideShieldPlus: Icon;

// ShieldQuestion aliases
export declare const ShieldQuestionIcon: Icon;
export declare const LucideShieldQuestion: Icon;

// ShieldX aliases
export declare const ShieldXIcon: Icon;
export declare const LucideShieldX: Icon;
export declare const ShieldClose: Icon;

// Shield aliases
export declare const ShieldIcon: Icon;
export declare const LucideShield: Icon;

// ShipWheel aliases
export declare const ShipWheelIcon: Icon;
export declare const LucideShipWheel: Icon;

// Ship aliases
export declare const ShipIcon: Icon;
export declare const LucideShip: Icon;

// Shirt aliases
export declare const ShirtIcon: Icon;
export declare const LucideShirt: Icon;

// ShoppingBag aliases
export declare const ShoppingBagIcon: Icon;
export declare const LucideShoppingBag: Icon;

// ShoppingBasket aliases
export declare const ShoppingBasketIcon: Icon;
export declare const LucideShoppingBasket: Icon;

// ShoppingCart aliases
export declare const ShoppingCartIcon: Icon;
export declare const LucideShoppingCart: Icon;

// Shovel aliases
export declare const ShovelIcon: Icon;
export declare const LucideShovel: Icon;

// ShowerHead aliases
export declare const ShowerHeadIcon: Icon;
export declare const LucideShowerHead: Icon;

// Shrink aliases
export declare const ShrinkIcon: Icon;
export declare const LucideShrink: Icon;

// Shrub aliases
export declare const ShrubIcon: Icon;
export declare const LucideShrub: Icon;

// Shuffle aliases
export declare const ShuffleIcon: Icon;
export declare const LucideShuffle: Icon;

// SigmaSquare aliases
export declare const SigmaSquareIcon: Icon;
export declare const LucideSigmaSquare: Icon;

// Sigma aliases
export declare const SigmaIcon: Icon;
export declare const LucideSigma: Icon;

// SignalHigh aliases
export declare const SignalHighIcon: Icon;
export declare const LucideSignalHigh: Icon;

// SignalLow aliases
export declare const SignalLowIcon: Icon;
export declare const LucideSignalLow: Icon;

// SignalMedium aliases
export declare const SignalMediumIcon: Icon;
export declare const LucideSignalMedium: Icon;

// SignalZero aliases
export declare const SignalZeroIcon: Icon;
export declare const LucideSignalZero: Icon;

// Signal aliases
export declare const SignalIcon: Icon;
export declare const LucideSignal: Icon;

// SignpostBig aliases
export declare const SignpostBigIcon: Icon;
export declare const LucideSignpostBig: Icon;

// Signpost aliases
export declare const SignpostIcon: Icon;
export declare const LucideSignpost: Icon;

// Siren aliases
export declare const SirenIcon: Icon;
export declare const LucideSiren: Icon;

// SkipBack aliases
export declare const SkipBackIcon: Icon;
export declare const LucideSkipBack: Icon;

// SkipForward aliases
export declare const SkipForwardIcon: Icon;
export declare const LucideSkipForward: Icon;

// Skull aliases
export declare const SkullIcon: Icon;
export declare const LucideSkull: Icon;

// Slack aliases
export declare const SlackIcon: Icon;
export declare const LucideSlack: Icon;

// Slash aliases
export declare const SlashIcon: Icon;
export declare const LucideSlash: Icon;

// Slice aliases
export declare const SliceIcon: Icon;
export declare const LucideSlice: Icon;

// SlidersHorizontal aliases
export declare const SlidersHorizontalIcon: Icon;
export declare const LucideSlidersHorizontal: Icon;

// Sliders aliases
export declare const SlidersIcon: Icon;
export declare const LucideSliders: Icon;

// SmartphoneCharging aliases
export declare const SmartphoneChargingIcon: Icon;
export declare const LucideSmartphoneCharging: Icon;

// SmartphoneNfc aliases
export declare const SmartphoneNfcIcon: Icon;
export declare const LucideSmartphoneNfc: Icon;

// Smartphone aliases
export declare const SmartphoneIcon: Icon;
export declare const LucideSmartphone: Icon;

// SmilePlus aliases
export declare const SmilePlusIcon: Icon;
export declare const LucideSmilePlus: Icon;

// Smile aliases
export declare const SmileIcon: Icon;
export declare const LucideSmile: Icon;

// Snail aliases
export declare const SnailIcon: Icon;
export declare const LucideSnail: Icon;

// Snowflake aliases
export declare const SnowflakeIcon: Icon;
export declare const LucideSnowflake: Icon;

// Sofa aliases
export declare const SofaIcon: Icon;
export declare const LucideSofa: Icon;

// Soup aliases
export declare const SoupIcon: Icon;
export declare const LucideSoup: Icon;

// Space aliases
export declare const SpaceIcon: Icon;
export declare const LucideSpace: Icon;

// Spade aliases
export declare const SpadeIcon: Icon;
export declare const LucideSpade: Icon;

// Sparkle aliases
export declare const SparkleIcon: Icon;
export declare const LucideSparkle: Icon;

// Sparkles aliases
export declare const SparklesIcon: Icon;
export declare const LucideSparkles: Icon;
export declare const Stars: Icon;

// Speaker aliases
export declare const SpeakerIcon: Icon;
export declare const LucideSpeaker: Icon;

// Speech aliases
export declare const SpeechIcon: Icon;
export declare const LucideSpeech: Icon;

// SpellCheck2 aliases
export declare const SpellCheck2Icon: Icon;
export declare const LucideSpellCheck2: Icon;

// SpellCheck aliases
export declare const SpellCheckIcon: Icon;
export declare const LucideSpellCheck: Icon;

// Spline aliases
export declare const SplineIcon: Icon;
export declare const LucideSpline: Icon;

// SplitSquareHorizontal aliases
export declare const SplitSquareHorizontalIcon: Icon;
export declare const LucideSplitSquareHorizontal: Icon;

// SplitSquareVertical aliases
export declare const SplitSquareVerticalIcon: Icon;
export declare const LucideSplitSquareVertical: Icon;

// Split aliases
export declare const SplitIcon: Icon;
export declare const LucideSplit: Icon;

// SprayCan aliases
export declare const SprayCanIcon: Icon;
export declare const LucideSprayCan: Icon;

// Sprout aliases
export declare const SproutIcon: Icon;
export declare const LucideSprout: Icon;

// SquareAsterisk aliases
export declare const SquareAsteriskIcon: Icon;
export declare const LucideSquareAsterisk: Icon;

// SquareCode aliases
export declare const SquareCodeIcon: Icon;
export declare const LucideSquareCode: Icon;

// SquareDashedBottomCode aliases
export declare const SquareDashedBottomCodeIcon: Icon;
export declare const LucideSquareDashedBottomCode: Icon;

// SquareDashedBottom aliases
export declare const SquareDashedBottomIcon: Icon;
export declare const LucideSquareDashedBottom: Icon;

// SquareDot aliases
export declare const SquareDotIcon: Icon;
export declare const LucideSquareDot: Icon;

// SquareEqual aliases
export declare const SquareEqualIcon: Icon;
export declare const LucideSquareEqual: Icon;

// SquareSlash aliases
export declare const SquareSlashIcon: Icon;
export declare const LucideSquareSlash: Icon;

// SquareStack aliases
export declare const SquareStackIcon: Icon;
export declare const LucideSquareStack: Icon;

// Square aliases
export declare const SquareIcon: Icon;
export declare const LucideSquare: Icon;

// Squirrel aliases
export declare const SquirrelIcon: Icon;
export declare const LucideSquirrel: Icon;

// Stamp aliases
export declare const StampIcon: Icon;
export declare const LucideStamp: Icon;

// StarHalf aliases
export declare const StarHalfIcon: Icon;
export declare const LucideStarHalf: Icon;

// StarOff aliases
export declare const StarOffIcon: Icon;
export declare const LucideStarOff: Icon;

// Star aliases
export declare const StarIcon: Icon;
export declare const LucideStar: Icon;

// StepBack aliases
export declare const StepBackIcon: Icon;
export declare const LucideStepBack: Icon;

// StepForward aliases
export declare const StepForwardIcon: Icon;
export declare const LucideStepForward: Icon;

// Stethoscope aliases
export declare const StethoscopeIcon: Icon;
export declare const LucideStethoscope: Icon;

// Sticker aliases
export declare const StickerIcon: Icon;
export declare const LucideSticker: Icon;

// StickyNote aliases
export declare const StickyNoteIcon: Icon;
export declare const LucideStickyNote: Icon;

// StopCircle aliases
export declare const StopCircleIcon: Icon;
export declare const LucideStopCircle: Icon;

// Store aliases
export declare const StoreIcon: Icon;
export declare const LucideStore: Icon;

// StretchHorizontal aliases
export declare const StretchHorizontalIcon: Icon;
export declare const LucideStretchHorizontal: Icon;

// StretchVertical aliases
export declare const StretchVerticalIcon: Icon;
export declare const LucideStretchVertical: Icon;

// Strikethrough aliases
export declare const StrikethroughIcon: Icon;
export declare const LucideStrikethrough: Icon;

// Subscript aliases
export declare const SubscriptIcon: Icon;
export declare const LucideSubscript: Icon;

// Subtitles aliases
export declare const SubtitlesIcon: Icon;
export declare const LucideSubtitles: Icon;

// SunDim aliases
export declare const SunDimIcon: Icon;
export declare const LucideSunDim: Icon;

// SunMedium aliases
export declare const SunMediumIcon: Icon;
export declare const LucideSunMedium: Icon;

// SunMoon aliases
export declare const SunMoonIcon: Icon;
export declare const LucideSunMoon: Icon;

// SunSnow aliases
export declare const SunSnowIcon: Icon;
export declare const LucideSunSnow: Icon;

// Sun aliases
export declare const SunIcon: Icon;
export declare const LucideSun: Icon;

// Sunrise aliases
export declare const SunriseIcon: Icon;
export declare const LucideSunrise: Icon;

// Sunset aliases
export declare const SunsetIcon: Icon;
export declare const LucideSunset: Icon;

// Superscript aliases
export declare const SuperscriptIcon: Icon;
export declare const LucideSuperscript: Icon;

// SwissFranc aliases
export declare const SwissFrancIcon: Icon;
export declare const LucideSwissFranc: Icon;

// SwitchCamera aliases
export declare const SwitchCameraIcon: Icon;
export declare const LucideSwitchCamera: Icon;

// Sword aliases
export declare const SwordIcon: Icon;
export declare const LucideSword: Icon;

// Swords aliases
export declare const SwordsIcon: Icon;
export declare const LucideSwords: Icon;

// Syringe aliases
export declare const SyringeIcon: Icon;
export declare const LucideSyringe: Icon;

// Table2 aliases
export declare const Table2Icon: Icon;
export declare const LucideTable2: Icon;

// TableProperties aliases
export declare const TablePropertiesIcon: Icon;
export declare const LucideTableProperties: Icon;

// Table aliases
export declare const TableIcon: Icon;
export declare const LucideTable: Icon;

// TabletSmartphone aliases
export declare const TabletSmartphoneIcon: Icon;
export declare const LucideTabletSmartphone: Icon;

// Tablet aliases
export declare const TabletIcon: Icon;
export declare const LucideTablet: Icon;

// Tablets aliases
export declare const TabletsIcon: Icon;
export declare const LucideTablets: Icon;

// Tag aliases
export declare const TagIcon: Icon;
export declare const LucideTag: Icon;

// Tags aliases
export declare const TagsIcon: Icon;
export declare const LucideTags: Icon;

// Tally1 aliases
export declare const Tally1Icon: Icon;
export declare const LucideTally1: Icon;

// Tally2 aliases
export declare const Tally2Icon: Icon;
export declare const LucideTally2: Icon;

// Tally3 aliases
export declare const Tally3Icon: Icon;
export declare const LucideTally3: Icon;

// Tally4 aliases
export declare const Tally4Icon: Icon;
export declare const LucideTally4: Icon;

// Tally5 aliases
export declare const Tally5Icon: Icon;
export declare const LucideTally5: Icon;

// Tangent aliases
export declare const TangentIcon: Icon;
export declare const LucideTangent: Icon;

// Target aliases
export declare const TargetIcon: Icon;
export declare const LucideTarget: Icon;

// TentTree aliases
export declare const TentTreeIcon: Icon;
export declare const LucideTentTree: Icon;

// Tent aliases
export declare const TentIcon: Icon;
export declare const LucideTent: Icon;

// TerminalSquare aliases
export declare const TerminalSquareIcon: Icon;
export declare const LucideTerminalSquare: Icon;

// Terminal aliases
export declare const TerminalIcon: Icon;
export declare const LucideTerminal: Icon;

// TestTube2 aliases
export declare const TestTube2Icon: Icon;
export declare const LucideTestTube2: Icon;

// TestTube aliases
export declare const TestTubeIcon: Icon;
export declare const LucideTestTube: Icon;

// TestTubes aliases
export declare const TestTubesIcon: Icon;
export declare const LucideTestTubes: Icon;

// TextCursorInput aliases
export declare const TextCursorInputIcon: Icon;
export declare const LucideTextCursorInput: Icon;

// TextCursor aliases
export declare const TextCursorIcon: Icon;
export declare const LucideTextCursor: Icon;

// TextQuote aliases
export declare const TextQuoteIcon: Icon;
export declare const LucideTextQuote: Icon;

// TextSelect aliases
export declare const TextSelectIcon: Icon;
export declare const LucideTextSelect: Icon;
export declare const TextSelection: Icon;

// Text aliases
export declare const TextIcon: Icon;
export declare const LucideText: Icon;

// Theater aliases
export declare const TheaterIcon: Icon;
export declare const LucideTheater: Icon;

// ThermometerSnowflake aliases
export declare const ThermometerSnowflakeIcon: Icon;
export declare const LucideThermometerSnowflake: Icon;

// ThermometerSun aliases
export declare const ThermometerSunIcon: Icon;
export declare const LucideThermometerSun: Icon;

// Thermometer aliases
export declare const ThermometerIcon: Icon;
export declare const LucideThermometer: Icon;

// ThumbsDown aliases
export declare const ThumbsDownIcon: Icon;
export declare const LucideThumbsDown: Icon;

// ThumbsUp aliases
export declare const ThumbsUpIcon: Icon;
export declare const LucideThumbsUp: Icon;

// Ticket aliases
export declare const TicketIcon: Icon;
export declare const LucideTicket: Icon;

// TimerOff aliases
export declare const TimerOffIcon: Icon;
export declare const LucideTimerOff: Icon;

// TimerReset aliases
export declare const TimerResetIcon: Icon;
export declare const LucideTimerReset: Icon;

// Timer aliases
export declare const TimerIcon: Icon;
export declare const LucideTimer: Icon;

// ToggleLeft aliases
export declare const ToggleLeftIcon: Icon;
export declare const LucideToggleLeft: Icon;

// ToggleRight aliases
export declare const ToggleRightIcon: Icon;
export declare const LucideToggleRight: Icon;

// Tornado aliases
export declare const TornadoIcon: Icon;
export declare const LucideTornado: Icon;

// Torus aliases
export declare const TorusIcon: Icon;
export declare const LucideTorus: Icon;

// TouchpadOff aliases
export declare const TouchpadOffIcon: Icon;
export declare const LucideTouchpadOff: Icon;

// Touchpad aliases
export declare const TouchpadIcon: Icon;
export declare const LucideTouchpad: Icon;

// TowerControl aliases
export declare const TowerControlIcon: Icon;
export declare const LucideTowerControl: Icon;

// ToyBrick aliases
export declare const ToyBrickIcon: Icon;
export declare const LucideToyBrick: Icon;

// Tractor aliases
export declare const TractorIcon: Icon;
export declare const LucideTractor: Icon;

// TrafficCone aliases
export declare const TrafficConeIcon: Icon;
export declare const LucideTrafficCone: Icon;

// TrainFrontTunnel aliases
export declare const TrainFrontTunnelIcon: Icon;
export declare const LucideTrainFrontTunnel: Icon;

// TrainFront aliases
export declare const TrainFrontIcon: Icon;
export declare const LucideTrainFront: Icon;

// TrainTrack aliases
export declare const TrainTrackIcon: Icon;
export declare const LucideTrainTrack: Icon;

// TramFront aliases
export declare const TramFrontIcon: Icon;
export declare const LucideTramFront: Icon;
export declare const Train: Icon;

// Trash2 aliases
export declare const Trash2Icon: Icon;
export declare const LucideTrash2: Icon;

// Trash aliases
export declare const TrashIcon: Icon;
export declare const LucideTrash: Icon;

// TreeDeciduous aliases
export declare const TreeDeciduousIcon: Icon;
export declare const LucideTreeDeciduous: Icon;

// TreePine aliases
export declare const TreePineIcon: Icon;
export declare const LucideTreePine: Icon;

// Trees aliases
export declare const TreesIcon: Icon;
export declare const LucideTrees: Icon;

// Trello aliases
export declare const TrelloIcon: Icon;
export declare const LucideTrello: Icon;

// TrendingDown aliases
export declare const TrendingDownIcon: Icon;
export declare const LucideTrendingDown: Icon;

// TrendingUp aliases
export declare const TrendingUpIcon: Icon;
export declare const LucideTrendingUp: Icon;

// TriangleRight aliases
export declare const TriangleRightIcon: Icon;
export declare const LucideTriangleRight: Icon;

// Triangle aliases
export declare const TriangleIcon: Icon;
export declare const LucideTriangle: Icon;

// Trophy aliases
export declare const TrophyIcon: Icon;
export declare const LucideTrophy: Icon;

// Truck aliases
export declare const TruckIcon: Icon;
export declare const LucideTruck: Icon;

// Turtle aliases
export declare const TurtleIcon: Icon;
export declare const LucideTurtle: Icon;

// Tv2 aliases
export declare const Tv2Icon: Icon;
export declare const LucideTv2: Icon;

// Tv aliases
export declare const TvIcon: Icon;
export declare const LucideTv: Icon;

// Twitch aliases
export declare const TwitchIcon: Icon;
export declare const LucideTwitch: Icon;

// Twitter aliases
export declare const TwitterIcon: Icon;
export declare const LucideTwitter: Icon;

// Type aliases
export declare const TypeIcon: Icon;
export declare const LucideType: Icon;

// UmbrellaOff aliases
export declare const UmbrellaOffIcon: Icon;
export declare const LucideUmbrellaOff: Icon;

// Umbrella aliases
export declare const UmbrellaIcon: Icon;
export declare const LucideUmbrella: Icon;

// Underline aliases
export declare const UnderlineIcon: Icon;
export declare const LucideUnderline: Icon;

// Undo2 aliases
export declare const Undo2Icon: Icon;
export declare const LucideUndo2: Icon;

// UndoDot aliases
export declare const UndoDotIcon: Icon;
export declare const LucideUndoDot: Icon;

// Undo aliases
export declare const UndoIcon: Icon;
export declare const LucideUndo: Icon;

// UnfoldHorizontal aliases
export declare const UnfoldHorizontalIcon: Icon;
export declare const LucideUnfoldHorizontal: Icon;

// UnfoldVertical aliases
export declare const UnfoldVerticalIcon: Icon;
export declare const LucideUnfoldVertical: Icon;

// Ungroup aliases
export declare const UngroupIcon: Icon;
export declare const LucideUngroup: Icon;

// Unlink2 aliases
export declare const Unlink2Icon: Icon;
export declare const LucideUnlink2: Icon;

// Unlink aliases
export declare const UnlinkIcon: Icon;
export declare const LucideUnlink: Icon;

// UnlockKeyhole aliases
export declare const UnlockKeyholeIcon: Icon;
export declare const LucideUnlockKeyhole: Icon;

// Unlock aliases
export declare const UnlockIcon: Icon;
export declare const LucideUnlock: Icon;

// Unplug aliases
export declare const UnplugIcon: Icon;
export declare const LucideUnplug: Icon;

// UploadCloud aliases
export declare const UploadCloudIcon: Icon;
export declare const LucideUploadCloud: Icon;

// Upload aliases
export declare const UploadIcon: Icon;
export declare const LucideUpload: Icon;

// Usb aliases
export declare const UsbIcon: Icon;
export declare const LucideUsb: Icon;

// User2 aliases
export declare const User2Icon: Icon;
export declare const LucideUser2: Icon;

// UserCheck2 aliases
export declare const UserCheck2Icon: Icon;
export declare const LucideUserCheck2: Icon;

// UserCheck aliases
export declare const UserCheckIcon: Icon;
export declare const LucideUserCheck: Icon;

// UserCircle2 aliases
export declare const UserCircle2Icon: Icon;
export declare const LucideUserCircle2: Icon;

// UserCircle aliases
export declare const UserCircleIcon: Icon;
export declare const LucideUserCircle: Icon;

// UserCog2 aliases
export declare const UserCog2Icon: Icon;
export declare const LucideUserCog2: Icon;

// UserCog aliases
export declare const UserCogIcon: Icon;
export declare const LucideUserCog: Icon;

// UserMinus2 aliases
export declare const UserMinus2Icon: Icon;
export declare const LucideUserMinus2: Icon;

// UserMinus aliases
export declare const UserMinusIcon: Icon;
export declare const LucideUserMinus: Icon;

// UserPlus2 aliases
export declare const UserPlus2Icon: Icon;
export declare const LucideUserPlus2: Icon;

// UserPlus aliases
export declare const UserPlusIcon: Icon;
export declare const LucideUserPlus: Icon;

// UserSquare2 aliases
export declare const UserSquare2Icon: Icon;
export declare const LucideUserSquare2: Icon;

// UserSquare aliases
export declare const UserSquareIcon: Icon;
export declare const LucideUserSquare: Icon;

// UserX2 aliases
export declare const UserX2Icon: Icon;
export declare const LucideUserX2: Icon;

// UserX aliases
export declare const UserXIcon: Icon;
export declare const LucideUserX: Icon;

// User aliases
export declare const UserIcon: Icon;
export declare const LucideUser: Icon;

// Users2 aliases
export declare const Users2Icon: Icon;
export declare const LucideUsers2: Icon;

// Users aliases
export declare const UsersIcon: Icon;
export declare const LucideUsers: Icon;

// UtensilsCrossed aliases
export declare const UtensilsCrossedIcon: Icon;
export declare const LucideUtensilsCrossed: Icon;

// Utensils aliases
export declare const UtensilsIcon: Icon;
export declare const LucideUtensils: Icon;

// UtilityPole aliases
export declare const UtilityPoleIcon: Icon;
export declare const LucideUtilityPole: Icon;

// Variable aliases
export declare const VariableIcon: Icon;
export declare const LucideVariable: Icon;

// Vegan aliases
export declare const VeganIcon: Icon;
export declare const LucideVegan: Icon;

// VenetianMask aliases
export declare const VenetianMaskIcon: Icon;
export declare const LucideVenetianMask: Icon;

// VibrateOff aliases
export declare const VibrateOffIcon: Icon;
export declare const LucideVibrateOff: Icon;

// Vibrate aliases
export declare const VibrateIcon: Icon;
export declare const LucideVibrate: Icon;

// VideoOff aliases
export declare const VideoOffIcon: Icon;
export declare const LucideVideoOff: Icon;

// Video aliases
export declare const VideoIcon: Icon;
export declare const LucideVideo: Icon;

// Videotape aliases
export declare const VideotapeIcon: Icon;
export declare const LucideVideotape: Icon;

// View aliases
export declare const ViewIcon: Icon;
export declare const LucideView: Icon;

// Voicemail aliases
export declare const VoicemailIcon: Icon;
export declare const LucideVoicemail: Icon;

// Volume1 aliases
export declare const Volume1Icon: Icon;
export declare const LucideVolume1: Icon;

// Volume2 aliases
export declare const Volume2Icon: Icon;
export declare const LucideVolume2: Icon;

// VolumeX aliases
export declare const VolumeXIcon: Icon;
export declare const LucideVolumeX: Icon;

// Volume aliases
export declare const VolumeIcon: Icon;
export declare const LucideVolume: Icon;

// Vote aliases
export declare const VoteIcon: Icon;
export declare const LucideVote: Icon;

// Wallet2 aliases
export declare const Wallet2Icon: Icon;
export declare const LucideWallet2: Icon;

// WalletCards aliases
export declare const WalletCardsIcon: Icon;
export declare const LucideWalletCards: Icon;

// Wallet aliases
export declare const WalletIcon: Icon;
export declare const LucideWallet: Icon;

// Wallpaper aliases
export declare const WallpaperIcon: Icon;
export declare const LucideWallpaper: Icon;

// Wand2 aliases
export declare const Wand2Icon: Icon;
export declare const LucideWand2: Icon;

// Wand aliases
export declare const WandIcon: Icon;
export declare const LucideWand: Icon;

// Warehouse aliases
export declare const WarehouseIcon: Icon;
export declare const LucideWarehouse: Icon;

// Watch aliases
export declare const WatchIcon: Icon;
export declare const LucideWatch: Icon;

// Waves aliases
export declare const WavesIcon: Icon;
export declare const LucideWaves: Icon;

// Waypoints aliases
export declare const WaypointsIcon: Icon;
export declare const LucideWaypoints: Icon;

// Webcam aliases
export declare const WebcamIcon: Icon;
export declare const LucideWebcam: Icon;

// Webhook aliases
export declare const WebhookIcon: Icon;
export declare const LucideWebhook: Icon;

// Weight aliases
export declare const WeightIcon: Icon;
export declare const LucideWeight: Icon;

// WheatOff aliases
export declare const WheatOffIcon: Icon;
export declare const LucideWheatOff: Icon;

// Wheat aliases
export declare const WheatIcon: Icon;
export declare const LucideWheat: Icon;

// WholeWord aliases
export declare const WholeWordIcon: Icon;
export declare const LucideWholeWord: Icon;

// WifiOff aliases
export declare const WifiOffIcon: Icon;
export declare const LucideWifiOff: Icon;

// Wifi aliases
export declare const WifiIcon: Icon;
export declare const LucideWifi: Icon;

// Wind aliases
export declare const WindIcon: Icon;
export declare const LucideWind: Icon;

// WineOff aliases
export declare const WineOffIcon: Icon;
export declare const LucideWineOff: Icon;

// Wine aliases
export declare const WineIcon: Icon;
export declare const LucideWine: Icon;

// Workflow aliases
export declare const WorkflowIcon: Icon;
export declare const LucideWorkflow: Icon;

// WrapText aliases
export declare const WrapTextIcon: Icon;
export declare const LucideWrapText: Icon;

// Wrench aliases
export declare const WrenchIcon: Icon;
export declare const LucideWrench: Icon;

// XCircle aliases
export declare const XCircleIcon: Icon;
export declare const LucideXCircle: Icon;

// XOctagon aliases
export declare const XOctagonIcon: Icon;
export declare const LucideXOctagon: Icon;

// XSquare aliases
export declare const XSquareIcon: Icon;
export declare const LucideXSquare: Icon;

// X aliases
export declare const XIcon: Icon;
export declare const LucideX: Icon;

// Youtube aliases
export declare const YoutubeIcon: Icon;
export declare const LucideYoutube: Icon;

// ZapOff aliases
export declare const ZapOffIcon: Icon;
export declare const LucideZapOff: Icon;

// Zap aliases
export declare const ZapIcon: Icon;
export declare const LucideZap: Icon;

// ZoomIn aliases
export declare const ZoomInIcon: Icon;
export declare const LucideZoomIn: Icon;

// ZoomOut aliases
export declare const ZoomOutIcon: Icon;
export declare const LucideZoomOut: Icon;

