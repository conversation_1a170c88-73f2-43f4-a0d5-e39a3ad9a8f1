export { analyze, AnalyzeOptions } from './analyze';
export * from './definition';
export { Reference } from './referencer/Reference';
export { Visitor } from './referencer/Visitor';
export { PatternVisitor, PatternVisitorCallback, PatternVisitorOptions, } from './referencer/PatternVisitor';
export * from './scope';
export { ScopeManager } from './ScopeManager';
export * from './variable';
//# sourceMappingURL=index.d.ts.map