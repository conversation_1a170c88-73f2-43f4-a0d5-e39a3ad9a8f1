/**
 * lucide-vue-next v0.292.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const ToggleRight = createLucideIcon("ToggleRightIcon", [
  [
    "rect",
    {
      width: "20",
      height: "12",
      x: "2",
      y: "6",
      rx: "6",
      ry: "6",
      key: "f2vt7d"
    }
  ],
  ["circle", { cx: "16", cy: "12", r: "2", key: "4ma0v8" }]
]);

export { ToggleRight as default };
//# sourceMappingURL=toggle-right.js.map
